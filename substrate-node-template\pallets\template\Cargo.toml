[package]
name = "afrachain-pallets"
description = "FRAME pallet template for defining custom runtime logic."
version = "0.1.0"
license = "Unlicense"
authors.workspace = true
homepage.workspace = true
repository.workspace = true
edition.workspace = true
publish = false

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[dependencies]
codec = { features = ["derive"], workspace = true }
frame-benchmarking = { optional = true, workspace = true }
frame-support.workspace = true
frame-system.workspace = true
scale-info = { features = ["derive"], workspace = true }

[dev-dependencies]
sp-core = { default-features = true, workspace = true }
sp-io = { default-features = true, workspace = true }
sp-runtime = { default-features = true, workspace = true }

[features]
default = ["std"]
std = [
	"codec/std",
	"frame-benchmarking?/std",
	"frame-support/std",
	"frame-system/std",
	"scale-info/std",
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
	"frame-support/runtime-benchmarks",
	"frame-system/runtime-benchmarks",
	"sp-runtime/runtime-benchmarks",
]
try-runtime = [
	"frame-support/try-runtime",
	"frame-system/try-runtime",
	"sp-runtime/try-runtime",
]
