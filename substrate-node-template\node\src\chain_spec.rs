use sc_service::ChainType;
use solochain_template_runtime::WASM_BINARY;

/// Specialized `ChainSpec`. This is a specialization of the general Substrate ChainSpec type.
pub type ChainSpec = sc_service::GenericChainSpec;

pub fn development_config() -> Result<ChainSpec, String> {
    Ok(ChainSpec::from_genesis(
        // Name of the chain
        "Afrachain Development".to_string(),
        // Unique ID of the chain
        "afrachain_dev".to_string(),
        ChainType::Development,
        development_config_genesis,
        // Bootnodes
        vec![],
        None,
        // Protocol ID (unique identifier for the chain)
        Some("afrachain"),
        None,
        // Properties (e.g., token symbol and decimals)
        Some(get_properties("AFRA", 18, 42)),  // Change "AFRA" to your desired token symbol, 18 decimals is standard
        Extensions {
            relay_chain: "rococo-local".into(),  // Or "westend-local" for testnets; leave for local dev
            para_id: 1000,
        },
	))
}

pub fn local_chain_spec() -> Result<ChainSpec, String> {
	Ok(ChainSpec::builder(
		WASM_BINARY.ok_or_else(|| "Development wasm not available".to_string())?,
		None,
	)
	.with_name("Local Testnet")
	.with_id("local_testnet")
	.with_chain_type(ChainType::Local)
	.with_genesis_config_preset_name(sp_genesis_builder::LOCAL_TESTNET_RUNTIME_PRESET)
	.build())
}
