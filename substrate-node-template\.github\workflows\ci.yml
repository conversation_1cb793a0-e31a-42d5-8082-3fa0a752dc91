name: CI

on:
  pull_request:
  push:
    branches:
      - main
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
    steps:
      - uses: actions/checkout@v4

      - if: contains(matrix.os, 'ubuntu')
        uses: ./.github/actions/free-disk-space
      - if: contains(matrix.os, 'ubuntu')
        uses: ./.github/actions/ubuntu-dependencies
      - if: contains(matrix.os, 'macos')
        uses: ./.github/actions/macos-dependencies

      - name: Build the template
        run: cargo build
        timeout-minutes: 90

      - name: Run clippy
        run: |
          SKIP_WASM_BUILD=1 cargo clippy --all-targets --locked --workspace --quiet
          SKIP_WASM_BUILD=1 cargo clippy --all-targets --all-features --locked --workspace --quiet
        timeout-minutes: 30

      - name: Run the tests
        run: SKIP_WASM_BUILD=1 cargo test
        timeout-minutes: 15

      - name: Build the docs
        run: SKIP_WASM_BUILD=1 cargo doc --workspace --no-deps
        timeout-minutes: 15

  run-node:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
    steps:
      - uses: actions/checkout@v4

      - if: contains(matrix.os, 'ubuntu')
        uses: ./.github/actions/free-disk-space
      - if: contains(matrix.os, 'ubuntu')
        uses: ./.github/actions/ubuntu-dependencies
      - if: contains(matrix.os, 'macos')
        uses: ./.github/actions/macos-dependencies

      - name: Build the node individually in release mode
        run: cargo build --package solochain-template-node --release
        timeout-minutes: 90

      - name: Make sure the node is producing blocks
        run: |
          ./target/release/solochain-template-node --dev 2>&1 | tee out.txt &
          until curl -s '127.0.0.1:9944'; do sleep 5; done
          until cat out.txt | grep -s "Imported #2"; do sleep 5; done
        shell: bash
        timeout-minutes: 5

  build-docker:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/free-disk-space

      - name: Build the Dockerfile
        run: docker build . -t polkadot-sdk-solochain-template
        timeout-minutes: 90
