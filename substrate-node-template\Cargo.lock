# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 4

[[package]]
name = "Inflector"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3"
dependencies = [
 "lazy_static",
 "regex",
]

[[package]]
name = "addr2line"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a76fd60b23679b7d19bd066031410fb7e458ccc5e958eb5c325888ce4baedc97"
dependencies = [
 "gimli 0.27.3",
]

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "gimli 0.31.1",
]

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array 0.14.7",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if",
 "cipher 0.4.4",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher 0.4.4",
 "ctr",
 "ghash",
 "subtle 2.6.1",
]

[[package]]
name = "ahash"
version = "0.8.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a15f179cd60c4584b8a8c596927aadc462e27f2ca70c04e0071964a73ba7a75"
dependencies = [
 "cfg-if",
 "getrandom 0.3.3",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "anstream"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8acc5369981196006228e28809f761875c0327210a891e941f4c683b3a99529b"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cc3b69f167a1ef2e161439aa98aed94e6028e5f9a59be9a6ffb47aef1651f9"

[[package]]
name = "anstyle-parse"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2d16507662817a6a20a9ea92df6652ee4f94f914589377d69f3b21bc5798a9"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79947af37f4177cfead1110013d678905c37501914fba0efea834c3fe9a8d60c"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3534e77181a9cc07539ad51f2141fe32f6c3ffd4df76db8ad92346b003ae4e"
dependencies = [
 "anstyle",
 "once_cell",
 "windows-sys 0.59.0",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "approx"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cab112f0a86d568ea0e627cc1d6be74a1e9cd55214684db5561995f6dad897c6"
dependencies = [
 "num-traits",
]

[[package]]
name = "aquamarine"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21cc1548309245035eb18aa7f0967da6bc65587005170c56e6ef2788a4cf3f4e"
dependencies = [
 "include_dir",
 "itertools 0.10.5",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ark-bls12-377"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb00293ba84f51ce3bd026bd0de55899c4e68f0a39a5728cebae3a73ffdc0a4f"
dependencies = [
 "ark-ec 0.4.2",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bls12-381"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c775f0d12169cba7aae4caeb547bb6a50781c7449a8aa53793827c9ec4abf488"
dependencies = [
 "ark-ec 0.4.2",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bls12-381"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3df4dcc01ff89867cd86b0da835f23c3f02738353aaee7dde7495af71363b8d5"
dependencies = [
 "ark-ec 0.5.0",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-ec"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defd9a439d56ac24968cca0571f598a61bc8c55f71d50a89cda591cb750670ba"
dependencies = [
 "ark-ff 0.4.2",
 "ark-poly 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
 "itertools 0.10.5",
 "num-traits",
 "zeroize",
]

[[package]]
name = "ark-ec"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43d68f2d516162846c1238e755a7c4d131b892b70cc70c471a8e3ca3ed818fce"
dependencies = [
 "ahash",
 "ark-ff 0.5.0",
 "ark-poly 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "educe",
 "fnv",
 "hashbrown 0.15.3",
 "itertools 0.13.0",
 "num-bigint",
 "num-integer",
 "num-traits",
 "zeroize",
]

[[package]]
name = "ark-ed-on-bls12-381-bandersnatch"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1786b2e3832f6f0f7c8d62d5d5a282f6952a1ab99981c54cd52b6ac1d8f02df5"
dependencies = [
 "ark-bls12-381 0.5.0",
 "ark-ec 0.5.0",
 "ark-ff 0.5.0",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm 0.4.2",
 "ark-ff-macros 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "digest 0.10.7",
 "itertools 0.10.5",
 "num-bigint",
 "num-traits",
 "paste",
 "rustc_version",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a177aba0ed1e0fbb62aa9f6d0502e9b46dad8c2eab04c14258a1212d2557ea70"
dependencies = [
 "ark-ff-asm 0.5.0",
 "ark-ff-macros 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "arrayvec 0.7.6",
 "digest 0.10.7",
 "educe",
 "itertools 0.13.0",
 "num-bigint",
 "num-traits",
 "paste",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-asm"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62945a2f7e6de02a31fe400aa489f0e0f5b2502e69f95f853adb82a96c7a6b60"
dependencies = [
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09be120733ee33f7693ceaa202ca41accd5653b779563608f1234f78ae07c4b3"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ark-poly"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d320bfc44ee185d899ccbadfa8bc31aab923ce1558716e1997a1e74057fe86bf"
dependencies = [
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
]

[[package]]
name = "ark-poly"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "579305839da207f02b89cd1679e50e67b4331e2f9294a57693e5051b7703fe27"
dependencies = [
 "ahash",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "educe",
 "fnv",
 "hashbrown 0.15.3",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-serialize-derive 0.4.2",
 "ark-std 0.4.0",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f4d068aaf107ebcd7dfb52bc748f8030e0fc930ac8e360146ca54c1203088f7"
dependencies = [
 "ark-serialize-derive 0.5.0",
 "ark-std 0.5.0",
 "arrayvec 0.7.6",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae3281bc6d0fd7e549af32b52511e1302185bd688fd3359fa36423346ff682ea"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-serialize-derive"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213888f660fddcca0d257e88e54ac05bca01885f258ccdf695bafd77031bb69d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-std"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "246a225cc6131e9ee4f24619af0f19d67761fff15d7ccc22e42b80846e69449a"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-transcript"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47c1c928edb9d8ff24cb5dcb7651d3a98494fff3099eee95c2404cd813a9139f"
dependencies = [
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "digest 0.10.7",
 "rand_core 0.6.4",
 "sha3",
]

[[package]]
name = "ark-vrf"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9501da18569b2afe0eb934fb7afd5a247d238b94116155af4dd068f319adfe6d"
dependencies = [
 "ark-bls12-381 0.5.0",
 "ark-ec 0.5.0",
 "ark-ed-on-bls12-381-bandersnatch",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "digest 0.10.7",
 "rand_chacha 0.3.1",
 "sha2 0.10.9",
 "w3f-ring-proof",
 "zeroize",
]

[[package]]
name = "array-bytes"
version = "6.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d5dde061bd34119e902bbb2d9b90c5692635cf59fb91d582c2b68043f1b8293"

[[package]]
name = "arrayref"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76a2e8124351fda1ef8aaaa3bbd7ebbcb486bbcd4225aca0aa0d84bb2db8fecb"

[[package]]
name = "arrayvec"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd9fd44efafa8690358b7408d253adf110036b88f55672a933f01d616ad9b1b9"
dependencies = [
 "nodrop",
]

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"

[[package]]
name = "asn1-rs"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5493c3bedbacf7fd7382c6346bbd66687d12bbaad3a89a2d2c303ee6cf20b048"
dependencies = [
 "asn1-rs-derive 0.5.1",
 "asn1-rs-impl",
 "displaydoc",
 "nom",
 "num-traits",
 "rusticata-macros",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "asn1-rs"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56624a96882bb8c26d61312ae18cb45868e5a9992ea73c58e45c3101e56a1e60"
dependencies = [
 "asn1-rs-derive 0.6.0",
 "asn1-rs-impl",
 "displaydoc",
 "nom",
 "num-traits",
 "rusticata-macros",
 "thiserror 2.0.12",
 "time",
]

[[package]]
name = "asn1-rs-derive"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "965c2d33e53cb6b267e148a4cb0760bc01f4904c1cd4bb4002a085bb016d1490"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure 0.13.2",
]

[[package]]
name = "asn1-rs-derive"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3109e49b1e4909e9db6515a30c633684d68cdeaa252f215214cb4fa1a5bfee2c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure 0.13.2",
]

[[package]]
name = "asn1-rs-impl"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b18050c2cd6fe86c3a76584ef5e0baf286d038cda203eb6223df2cc413565f7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-channel"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81953c529336010edd6d8e358f886d9581267795c61b19475b71314bffa46d35"
dependencies = [
 "concurrent-queue",
 "event-listener 2.5.3",
 "futures-core",
]

[[package]]
name = "async-channel"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b47800b0be77592da0afd425cc03468052844aff33b84e33cc696f64e77b6a"
dependencies = [
 "concurrent-queue",
 "event-listener-strategy",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-executor"
version = "1.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb812ffb58524bdd10860d7d974e2f01cc0950c2438a74ee5ec2e2280c6c4ffa"
dependencies = [
 "async-task",
 "concurrent-queue",
 "fastrand",
 "futures-lite",
 "pin-project-lite",
 "slab",
]

[[package]]
name = "async-fs"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebcd09b382f40fcd159c2d695175b2ae620ffa5f3bd6f664131efff4e8b9e04a"
dependencies = [
 "async-lock",
 "blocking",
 "futures-lite",
]

[[package]]
name = "async-io"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a2b323ccce0a1d90b449fd71f2a06ca7faa7c54c2751f06c9bd851fc061059"
dependencies = [
 "async-lock",
 "cfg-if",
 "concurrent-queue",
 "futures-io",
 "futures-lite",
 "parking",
 "polling",
 "rustix 0.38.44",
 "slab",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "async-lock"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff6e472cdea888a4bd64f342f09b3f50e1886d32afe8df3d663c01140b811b18"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "pin-project-lite",
]

[[package]]
name = "async-net"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b948000fad4873c1c9339d60f2623323a0cfd3816e5181033c6a5cb68b2accf7"
dependencies = [
 "async-io",
 "blocking",
 "futures-lite",
]

[[package]]
name = "async-process"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63255f1dc2381611000436537bbedfe83183faa303a5a0edaf191edef06526bb"
dependencies = [
 "async-channel 2.3.1",
 "async-io",
 "async-lock",
 "async-signal",
 "async-task",
 "blocking",
 "cfg-if",
 "event-listener 5.4.0",
 "futures-lite",
 "rustix 0.38.44",
 "tracing",
]

[[package]]
name = "async-signal"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "637e00349800c0bdf8bfc21ebbc0b6524abea702b0da4168ac00d070d0c0b9f3"
dependencies = [
 "async-io",
 "async-lock",
 "atomic-waker",
 "cfg-if",
 "futures-core",
 "futures-io",
 "rustix 0.38.44",
 "signal-hook-registry",
 "slab",
 "windows-sys 0.59.0",
]

[[package]]
name = "async-task"
version = "4.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b75356056920673b02621b35afd0f7dda9306d03c79a30f5c56c44cf256e3de"

[[package]]
name = "async-trait"
version = "0.1.88"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e539d3fca749fcee5236ab05e93a52867dd549cc157c8cb7f99595f3cedffdb5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "asynchronous-codec"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4057f2c32adbb2fc158e22fb38433c8e9bbf76b75a4732c7c0cbaf695fb65568"
dependencies = [
 "bytes",
 "futures-sink",
 "futures-util",
 "memchr",
 "pin-project-lite",
]

[[package]]
name = "asynchronous-codec"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a860072022177f903e59730004fb5dc13db9275b79bb2aef7ba8ce831956c233"
dependencies = [
 "bytes",
 "futures-sink",
 "futures-util",
 "memchr",
 "pin-project-lite",
]

[[package]]
name = "atomic-take"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8ab6b55fe97976e46f91ddbed8d147d966475dc29b2032757ba47e02376fbc3"

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "attohttpc"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d9a9bf8b79a749ee0b911b91b671cc2b6c670bdbc7e3dfd537576ddc94bb2a2"
dependencies = [
 "http 0.2.12",
 "log",
 "url",
]

[[package]]
name = "autocfg"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace50bade8e6234aa140d9a2f552bbee1db4d353f69b8217bc503490fc1a9f26"

[[package]]
name = "backtrace"
version = "0.3.75"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6806a6321ec58106fea15becdad98371e28d92ccbc7c8f1b3b6dd724fe8f1002"
dependencies = [
 "addr2line 0.24.2",
 "cfg-if",
 "libc",
 "miniz_oxide",
 "object 0.36.7",
 "rustc-demangle",
 "windows-targets 0.52.6",
]

[[package]]
name = "base-x"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cbbc9d0964165b47557570cce6c952866c2678457aca742aafc9fb771d30270"

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base58"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6107fe1be6682a68940da878d9e9f5e90ca5745b3dec9fd1bb393c8777d4f581"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64ct"
version = "1.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89e25b6adfb930f02d1981565a6e5d9c547ac15a96606256d3b59040e5cd4ca3"

[[package]]
name = "binary-merkle-tree"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "181f5380e435b8ba6d901f8b16fc8908c6f0f8bea8973113d1c8718d89bb1809"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.65.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfdf7b466f9a4903edc73f95d6d2bcd5baf8ae620638762244d3f60143643cc5"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "prettyplease",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.101",
]

[[package]]
name = "bip32"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db40d3dfbeab4e031d78c844642fa0caa0b0db11ce1607ac9d2986dff1405c69"
dependencies = [
 "bs58",
 "hmac 0.12.1",
 "k256",
 "rand_core 0.6.4",
 "ripemd",
 "secp256k1 0.27.0",
 "sha2 0.10.9",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "bip39"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33415e24172c1b7d6066f6d999545375ab8e1d95421d6784bdfff9496f292387"
dependencies = [
 "bitcoin_hashes 0.13.0",
 "serde",
 "unicode-normalization",
]

[[package]]
name = "bitcoin-internals"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9425c3bf7089c983facbae04de54513cce73b41c7f9ff8c845b54e7bc64ebbfb"

[[package]]
name = "bitcoin-io"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b47c4ab7a93edb0c7198c5535ed9b52b63095f4e9b45279c6736cec4b856baf"

[[package]]
name = "bitcoin_hashes"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1930a4dabfebb8d7d9992db18ebe3ae2876f0a305fab206fd168df931ede293b"
dependencies = [
 "bitcoin-internals",
 "hex-conservative 0.1.2",
]

[[package]]
name = "bitcoin_hashes"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb18c03d0db0247e147a21a6faafd5a7eb851c743db062de72018b6b7e8e4d16"
dependencies = [
 "bitcoin-io",
 "hex-conservative 0.2.1",
]

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c8214115b7bf84099f1309324e63141d4c5d7cc26862f97a0a857dbefe165bd"

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "serde",
 "tap",
 "wyz",
]

[[package]]
name = "blake2"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94cb07b0da6a73955f8fb85d24c466778e70cda767a568229b104f0264089330"
dependencies = [
 "byte-tools",
 "crypto-mac 0.7.0",
 "digest 0.8.1",
 "opaque-debug 0.2.3",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "blake2-rfc"
version = "0.2.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d6d530bdd2d52966a6d03b7a964add7ae1a288d25214066fd4b600f0f796400"
dependencies = [
 "arrayvec 0.4.12",
 "constant_time_eq 0.1.5",
]

[[package]]
name = "blake2b_simd"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06e903a20b159e944f91ec8499fe1e55651480c541ea0a584f5d967c49ad9d99"
dependencies = [
 "arrayref",
 "arrayvec 0.7.6",
 "constant_time_eq 0.3.1",
]

[[package]]
name = "blake2s_simd"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e90f7deecfac93095eb874a40febd69427776e24e1bd7f87f33ac62d6f0174df"
dependencies = [
 "arrayref",
 "arrayvec 0.7.6",
 "constant_time_eq 0.3.1",
]

[[package]]
name = "blake3"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3888aaa89e4b2a40fca9848e400f6a658a5a3978de7be858e209cafa8be9a4a0"
dependencies = [
 "arrayref",
 "arrayvec 0.7.6",
 "cc",
 "cfg-if",
 "constant_time_eq 0.3.1",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "blocking"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "703f41c54fc768e63e091340b424302bb1c29ef4aa0c7f10fe849dfb114d29ea"
dependencies = [
 "async-channel 2.3.1",
 "async-task",
 "futures-io",
 "futures-lite",
 "piper",
]

[[package]]
name = "bounded-collections"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ad8a0bed7827f0b07a5d23cec2e58cc02038a99e4ca81616cb2bb2025f804d"
dependencies = [
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "bounded-vec"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68534a48cbf63a4b1323c433cf21238c9ec23711e0df13b08c33e5c2082663ce"
dependencies = [
 "thiserror 1.0.69",
]

[[package]]
name = "bs58"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf88ba1141d185c399bee5288d850d63b8369520c1eafc32a0430b5b6c287bf4"
dependencies = [
 "sha2 0.10.9",
 "tinyvec",
]

[[package]]
name = "build-helper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdce191bf3fa4995ce948c8c83b4640a1745457a149e73c6db75b4ffe36aad5f"
dependencies = [
 "semver 0.6.0",
]

[[package]]
name = "bumpalo"
version = "3.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1628fb46dfa0b37568d12e5edd512553eccf6a22a78e8bde00bb4aed84d5bdbf"

[[package]]
name = "byte-slice-cast"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7575182f7272186991736b70173b0ea045398f984bf5ebbb3804736ce1330c9d"

[[package]]
name = "byte-tools"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3b5ca7a04898ad4bcd41c90c5285445ff5b791899bb1b0abdd2a2aa791211d7"

[[package]]
name = "bytemuck"
version = "1.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9134a6ef01ce4b366b50689c94f82c14bc72bc5d0386829828a2e2752ef7958c"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"

[[package]]
name = "bzip2-sys"
version = "0.1.13+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225bff33b2141874fe80d71e07d6eec4f85c5c216453dd96388240f96e1acc14"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "c2-chacha"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d27dae93fe7b1e0424dc57179ac396908c26b035a87234809f5c4dfd1b47dc80"
dependencies = [
 "cipher 0.2.5",
 "ppv-lite86",
]

[[package]]
name = "camino"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b96ec4966b5813e2c0507c1f86115c8c5abaadc3980879c3424042a02fd1ad3"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eee4243f1f26fc7a42710e7439c149e2b10b05472f88090acce52632f231a73a"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.26",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "cc"
version = "1.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32db95edf998450acc7881c932f94cd9b05c87b4b2599e8bab064753da4acfd1"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cesu8"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d43a04d8753f35258c91f8ec639f792891f748a1edbd759cf1dcea3382ad83c"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-expr"
version = "0.15.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d067ad48b8650848b989a59a86c6c36a995d02d2bf778d45c3c5d57bc2718f02"
dependencies = [
 "smallvec",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd16c4719339c4530435d38e511904438d07cce7950afa3718a84ac36c10e89e"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "chacha"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddf3c081b5fba1e5615640aae998e0fbd10c24cbd897ee39ed754a77601a4862"
dependencies = [
 "byteorder",
 "keystream",
]

[[package]]
name = "chacha20"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3613f74bd2eac03dad61bd53dbe620703d4371614fe0bc3b9f04dd36fe4e818"
dependencies = [
 "cfg-if",
 "cipher 0.4.4",
 "cpufeatures",
]

[[package]]
name = "chacha20poly1305"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10cd79432192d1c0f4e1a0fef9527696cc039165d729fb41b3f4f4f354c2dc35"
dependencies = [
 "aead",
 "chacha20",
 "cipher 0.4.4",
 "poly1305",
 "zeroize",
]

[[package]]
name = "chrono"
version = "0.4.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c469d952047f47f91b68d1cba3f10d63c11d73e4636f24f08daf0278abf01c4d"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "wasm-bindgen",
 "windows-link",
]

[[package]]
name = "cid"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9b68e3193982cd54187d71afdb2a271ad4cf8af157858e9cb911b91321de143"
dependencies = [
 "core2",
 "multibase",
 "multihash 0.17.0",
 "serde",
 "unsigned-varint 0.7.2",
]

[[package]]
name = "cid"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3147d8272e8fa0ccd29ce51194dd98f79ddfb8191ba9e3409884e751798acf3a"
dependencies = [
 "core2",
 "multibase",
 "multihash 0.19.3",
 "unsigned-varint 0.8.0",
]

[[package]]
name = "cipher"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12f8e7987cbd042a63249497f41aed09f8e65add917ea6566effbc56578d6801"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
 "zeroize",
]

[[package]]
name = "clang-sys"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b023947811758c97c59bf9d1c188fd619ad4718dcaa767947df1cadb14f39f4"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.5.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed93b9805f8ba930df42c2590f05453d5ec36cbb85d018868a5b24d31f6ac000"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "379026ff283facf611b0ea629334361c4211d1b12ee01024eec1591133b04120"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
 "terminal_size",
]

[[package]]
name = "clap_derive"
version = "4.5.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09176aae279615badda0765c0c0b3f6ed53f4709118af73cf4655d85d1530cd7"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "clap_lex"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46ad14479a25103f283c0f10005961cf086d8dc42205bb44c46ac563475dca6"

[[package]]
name = "coarsetime"
version = "0.1.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91849686042de1b41cd81490edc83afbcb0abe5a9b6f2c4114f23ce8cca1bcf4"
dependencies = [
 "libc",
 "wasix",
 "wasm-bindgen",
]

[[package]]
name = "codespan-reporting"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe6d2e5af09e8c8ad56c969f2157a3d4238cebc7c55f0a517728c38f7b200f81"
dependencies = [
 "serde",
 "termcolor",
 "unicode-width",
]

[[package]]
name = "colorchoice"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b63caa9aa9397e2d9480a9b13673856c78d8ac123288526c37d7839f2a86990"

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes",
 "memchr",
]

[[package]]
name = "comfy-table"
version = "7.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a65ebfec4fb190b6f90e944a817d60499ee0744e582530e2c9900a22e591d9a"
dependencies = [
 "unicode-segmentation",
 "unicode-width",
]

[[package]]
name = "common-path"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2382f75942f4b3be3690fe4f86365e9c853c1587d6ee58212cebf6e2a9ccd101"

[[package]]
name = "concurrent-queue"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ca0197aee26d1ae37445ee532fefce43251d24cc7c166799f4d46817f1d3973"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "console"
version = "0.15.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "054ccb5b10f9f2cbf51eb355ca1d05c2d279ce1804688d0db74b4733a5aeafd8"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width",
 "windows-sys 0.59.0",
]

[[package]]
name = "const-hex"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b0485bab839b018a8f1723fc5391819fea5f8f0f32288ef8a735fd096b6160c"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "hex",
 "proptest",
 "serde",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-random"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87e00182fe74b066627d63b85fd550ac2998d4b0bd86bfed477a0ae4c7c71359"
dependencies = [
 "const-random-macro",
]

[[package]]
name = "const-random-macro"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d839f2a20b0aee515dc581a6172f2321f96cab76c1a38a4c584a194955390e"
dependencies = [
 "getrandom 0.2.16",
 "once_cell",
 "tiny-keccak",
]

[[package]]
name = "const_format"
version = "0.2.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126f97965c8ad46d6d9163268ff28432e8f6a1196a55578867832e3049df63dd"
dependencies = [
 "const_format_proc_macros",
]

[[package]]
name = "const_format_proc_macros"
version = "0.2.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d57c2eccfb16dbac1f4e61e206105db5820c9d26c3c472bc17c774259ef7744"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-xid",
]

[[package]]
name = "constant_time_eq"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "245097e9a4535ee1e3e3931fcfcd55a796a44c643e8596ff6566d68f09b87bbc"

[[package]]
name = "constant_time_eq"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c74b8349d32d297c9134b8c88677813a227df8f779daa29bfc29c183fe3dca6"

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b55271e5c8c478ad3f38ad24ef34923091e0548492a266d19b3c0b4d82574c63"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773648b94d0e5d620f64f280777445740e61fe701025087ec8b57f45c791888b"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpp_demangle"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeaa953eaad386a53111e47172c2fedba671e5684c8dd601a5f474f4f118710f"
dependencies = [
 "cfg-if",
]

[[package]]
name = "cpufeatures"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ed5838eebb26a2bb2e58f6d5b5316989ae9d08bab10e0e6d103e656d1b0280"
dependencies = [
 "libc",
]

[[package]]
name = "cranelift-bforest"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1277fbfa94bc82c8ec4af2ded3e639d49ca5f7f3c7eeab2c66accd135ece4e70"
dependencies = [
 "cranelift-entity",
]

[[package]]
name = "cranelift-codegen"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6e8c31ad3b2270e9aeec38723888fe1b0ace3bea2b06b3f749ccf46661d3220"
dependencies = [
 "bumpalo",
 "cranelift-bforest",
 "cranelift-codegen-meta",
 "cranelift-codegen-shared",
 "cranelift-entity",
 "cranelift-isle",
 "gimli 0.27.3",
 "hashbrown 0.13.2",
 "log",
 "regalloc2 0.6.1",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-codegen-meta"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8ac5ac30d62b2d66f12651f6b606dbdfd9c2cfd0908de6b387560a277c5c9da"
dependencies = [
 "cranelift-codegen-shared",
]

[[package]]
name = "cranelift-codegen-shared"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd82b8b376247834b59ed9bdc0ddeb50f517452827d4a11bccf5937b213748b8"

[[package]]
name = "cranelift-entity"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40099d38061b37e505e63f89bab52199037a72b931ad4868d9089ff7268660b0"
dependencies = [
 "serde",
]

[[package]]
name = "cranelift-frontend"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64a25d9d0a0ae3079c463c34115ec59507b4707175454f0eee0891e83e30e82d"
dependencies = [
 "cranelift-codegen",
 "log",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-isle"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80de6a7d0486e4acbd5f9f87ec49912bf4c8fb6aea00087b989685460d4469ba"

[[package]]
name = "cranelift-native"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6b03e0e03801c4b3fd8ce0758a94750c07a44e7944cc0ffbf0d3f2e7c79b00"
dependencies = [
 "cranelift-codegen",
 "libc",
 "target-lexicon",
]

[[package]]
name = "cranelift-wasm"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff3220489a3d928ad91e59dd7aeaa8b3de18afb554a6211213673a71c90737ac"
dependencies = [
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "itertools 0.10.5",
 "log",
 "smallvec",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f58bbc28f91df819d0aa2a2c00cd19754769c2fad90579b3592b1c9ba7a3115"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crunchy"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43da5946c66ffcc7745f48db692ffbb10a83bfe0afd96235c5c2a4fb23994929"

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4434400df11d95d556bac068ddfedd482915eb18fe8bea89bc80b6e4b1c179e5"
dependencies = [
 "generic-array 0.12.4",
 "subtle 1.0.0",
]

[[package]]
name = "crypto-mac"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b584a330336237c1eecd3e94266efb216c56ed91225d634cb2991c5f3fd1aeab"
dependencies = [
 "generic-array 0.14.7",
 "subtle 2.6.1",
]

[[package]]
name = "crypto_secretbox"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9d6cf87adf719ddf43a805e92c6870a531aedda35ff640442cbaf8674e141e1"
dependencies = [
 "aead",
 "cipher 0.4.4",
 "generic-array 0.14.7",
 "poly1305",
 "salsa20",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher 0.4.4",
]

[[package]]
name = "cumulus-client-parachain-inherent"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4acde48ac4c352f41a1eef209a8bc7dd76d5d6dad2979aa6527678beda7b2f7"
dependencies = [
 "async-trait",
 "cumulus-primitives-core",
 "cumulus-primitives-parachain-inherent",
 "cumulus-relay-chain-interface",
 "cumulus-test-relay-sproof-builder",
 "parity-scale-codec",
 "sc-client-api",
 "sp-crypto-hashing",
 "sp-inherents",
 "sp-runtime",
 "sp-state-machine",
 "sp-storage",
 "tracing",
]

[[package]]
name = "cumulus-primitives-core"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f9e219ac5b7cc1ec53c8c3fc01745ec28d77ddd845dc8b9c32e542d70f11888"
dependencies = [
 "parity-scale-codec",
 "polkadot-core-primitives",
 "polkadot-parachain-primitives",
 "polkadot-primitives",
 "scale-info",
 "sp-api",
 "sp-runtime",
 "sp-trie",
 "staging-xcm",
]

[[package]]
name = "cumulus-primitives-parachain-inherent"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56c8bb6be20c760997a62ee067fc63be701b15cac32adc8526f0eefc4623a887"
dependencies = [
 "async-trait",
 "cumulus-primitives-core",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-inherents",
 "sp-trie",
]

[[package]]
name = "cumulus-primitives-proof-size-hostfunction"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9230c15cefe5c80941ac287e3c6a900631de4d673ff167fe622f1698c97a845e"
dependencies = [
 "sp-externalities",
 "sp-runtime-interface",
 "sp-trie",
]

[[package]]
name = "cumulus-relay-chain-interface"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fcdead0c8d5939349b712e863d6996459ddc2b2b021b1c1386dd5bcd0a1ac14"
dependencies = [
 "async-trait",
 "cumulus-primitives-core",
 "futures",
 "jsonrpsee-core",
 "parity-scale-codec",
 "polkadot-overseer",
 "sc-client-api",
 "sp-api",
 "sp-blockchain",
 "sp-state-machine",
 "sp-version",
 "thiserror 1.0.69",
]

[[package]]
name = "cumulus-test-relay-sproof-builder"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1bf30f2eed8f8bfd89e65d52395d124d45caa4ccd90a7e1326bb2fb7ac347b2"
dependencies = [
 "cumulus-primitives-core",
 "parity-scale-codec",
 "polkadot-primitives",
 "sp-runtime",
 "sp-state-machine",
 "sp-trie",
]

[[package]]
name = "curve25519-dalek"
version = "4.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fb8b7c4503de7d6ae7b42ab72a5a59857b4c937ec27a3d4539dba95b5ab2be"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "curve25519-dalek-derive",
 "digest 0.10.7",
 "fiat-crypto",
 "rustc_version",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46882e17999c6cc590af592290432be3bce0428cb0d5f8b6715e4dc7b383eb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "cxx"
version = "1.0.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a71ea7f29c73f7ffa64c50b83c9fe4d3a6d4be89a86b009eb80d5a6d3429d741"
dependencies = [
 "cc",
 "cxxbridge-cmd",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "foldhash",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36a8232661d66dcf713394726157d3cfe0a89bfc85f52d6e9f9bbc2306797fe7"
dependencies = [
 "cc",
 "codespan-reporting",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.101",
]

[[package]]
name = "cxxbridge-cmd"
version = "1.0.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f44296c8693e9ea226a48f6a122727f77aa9e9e338380cb021accaeeb7ee279"
dependencies = [
 "clap",
 "codespan-reporting",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c42f69c181c176981ae44ba9876e2ea41ce8e574c296b38d06925ce9214fb8e4"

[[package]]
name = "cxxbridge-macro"
version = "1.0.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8faff5d4467e0709448187df29ccbf3b0982cc426ee444a193f87b11afb565a8"
dependencies = [
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "darling"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc7f46116c46ff9ab3eb1597a45688b6715c6e628b5c133e288e709a29bcb4ee"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d00b9596d185e565c2207a0b01f8bd1a135483d02d9b7b0a54b11da8d53412e"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim",
 "syn 2.0.101",
]

[[package]]
name = "darling_macro"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc34b93ccb385b40dc71c6fceac4b2ad23662c7eeb248cf10d529b7e055b6ead"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dashmap"
version = "5.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "978747c1d849a7d2ee5e8adc0159961c48fb7e5db2f06af6723b80123bb53856"
dependencies = [
 "cfg-if",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "data-encoding"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2330da5de22e8a3cb63252ce2abb30116bf5265e89c0e01bc17015ce30a476"

[[package]]
name = "data-encoding-macro"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47ce6c96ea0102f01122a185683611bd5ac8d99e62bc59dd12e6bda344ee673d"
dependencies = [
 "data-encoding",
 "data-encoding-macro-internal",
]

[[package]]
name = "data-encoding-macro-internal"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d162beedaa69905488a8da94f5ac3edb4dd4788b732fadb7bd120b2625c1976"
dependencies = [
 "data-encoding",
 "syn 2.0.101",
]

[[package]]
name = "der"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7c1832837b905bbfb5101e07cc24c8deddf52f93225eee6ead5f4d63d53ddcb"
dependencies = [
 "const-oid",
 "zeroize",
]

[[package]]
name = "der-parser"
version = "9.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cd0a5c643689626bec213c4d8bd4d96acc8ffdb4ad4bb6bc16abf27d5f4b553"
dependencies = [
 "asn1-rs 0.6.2",
 "displaydoc",
 "nom",
 "num-bigint",
 "num-traits",
 "rusticata-macros",
]

[[package]]
name = "der-parser"
version = "10.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07da5016415d5a3c4dd39b11ed26f915f52fc4e0dc197d87908bc916e51bc1a6"
dependencies = [
 "asn1-rs 0.7.1",
 "displaydoc",
 "nom",
 "num-bigint",
 "num-traits",
 "rusticata-macros",
]

[[package]]
name = "deranged"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e6a11ca8224451684bc0d7d5a7adbf8f2fd6887261a1cfc3c0432f9d4068e"
dependencies = [
 "powerfmt",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-syn-parse"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d65d7ce8132b7c0e54497a4d9a55a1c2a0912a0d786cf894472ba818fba45762"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "derive-where"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e73f2692d4bd3cac41dca28934a39894200c9fabf49586d77d0e5954af1d7902"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "derive_more"
version = "0.99.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6edb4b64a43d977b8e99788fe3a04d483834fba1215a7e02caa415b626497f7f"
dependencies = [
 "convert_case",
 "proc-macro2",
 "quote",
 "rustc_version",
 "syn 2.0.101",
]

[[package]]
name = "derive_more"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b99b9cbbe49445b21764dc0625032a89b145a2642e67603e1c936f5458d05"
dependencies = [
 "derive_more-impl",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7330aeadfbe296029522e6c40f315320aba36fc43a5b3632f3795348f3bd22"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "unicode-xid",
]

[[package]]
name = "digest"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3d0c8c8752312f9713efd397ff63acb9f85585afbf179282e720e7704954dd5"
dependencies = [
 "generic-array 0.12.4",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "const-oid",
 "crypto-common",
 "subtle 2.6.1",
]

[[package]]
name = "directories"
version = "5.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a49173b84e034382284f27f1af4dcbbd231ffa358c0fe316541a7337f376a35"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "directories-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339ee130d97a610ea5a5872d2bbb130fdf68884ff09d3028b81bec8a1ac23bbc"
dependencies = [
 "cfg-if",
 "dirs-sys-next",
]

[[package]]
name = "dirs"
version = "5.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44c45a9d03d6676652bcb5e724c7e988de1acad23a711b5217ab9cbecbec2225"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "dirs-sys"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "520f05a5cbd335fae5a99ff7a6ab8627577660ee5cfd6a94a6a929b52ff0321c"
dependencies = [
 "libc",
 "option-ext",
 "redox_users",
 "windows-sys 0.48.0",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "docify"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a772b62b1837c8f060432ddcc10b17aae1453ef17617a99bc07789252d2a5896"
dependencies = [
 "docify_macros",
]

[[package]]
name = "docify_macros"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60e6be249b0a462a14784a99b19bf35a667bb5e09de611738bb7362fa4c95ff7"
dependencies = [
 "common-path",
 "derive-syn-parse",
 "once_cell",
 "proc-macro2",
 "quote",
 "regex",
 "syn 2.0.101",
 "termcolor",
 "toml 0.8.22",
 "walkdir",
]

[[package]]
name = "downcast"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1435fa1053d8b2fbbe9be7e97eca7f33d37b28409959813daefc1446a14247f1"

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "dtoa"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6add3b8cff394282be81f3fc1a0605db594ed69890078ca6e2cab1c408bcf04"

[[package]]
name = "dyn-clonable"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a36efbb9bfd58e1723780aa04b61aba95ace6a05d9ffabfdb0b43672552f0805"
dependencies = [
 "dyn-clonable-impl",
 "dyn-clone",
]

[[package]]
name = "dyn-clonable-impl"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e8671d54058979a37a26f3511fbf8d198ba1aa35ffb202c42587d918d77213a"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dyn-clone"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c7a8fb8a9fbf66c1f703fe16184d10ca0ee9d23be5b4436400408ba54a95005"

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der",
 "digest 0.10.7",
 "elliptic-curve",
 "rfc6979",
 "serdect",
 "signature",
 "spki",
]

[[package]]
name = "ed25519"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "115531babc129696a58c64a4fef0a8bf9e9698629fb97e9e40767d235cfbcd53"
dependencies = [
 "pkcs8",
 "signature",
]

[[package]]
name = "ed25519-dalek"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a3daa8e81a3963a60642bcc1f90a670680bd4a77535faa384e9d1c79d620871"
dependencies = [
 "curve25519-dalek",
 "ed25519",
 "rand_core 0.6.4",
 "serde",
 "sha2 0.10.9",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "ed25519-zebra"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d9ce6874da5d4415896cd45ffbc4d1cfc0c4f9c079427bd870742c30f2f65a9"
dependencies = [
 "curve25519-dalek",
 "ed25519",
 "hashbrown 0.14.5",
 "hex",
 "rand_core 0.6.4",
 "sha2 0.10.9",
 "zeroize",
]

[[package]]
name = "educe"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7bc049e1bd8cdeb31b68bbd586a9464ecf9f3944af3958a7a9d0f8b9799417"
dependencies = [
 "enum-ordinalize",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct",
 "crypto-bigint",
 "digest 0.10.7",
 "ff",
 "generic-array 0.14.7",
 "group",
 "pkcs8",
 "rand_core 0.6.4",
 "sec1",
 "serdect",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "enum-as-inner"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1e6a265c649f3f5979b601d26f1d05ada116434c87741c9493cb56218f76cbc"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "enum-ordinalize"
version = "4.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea0dcfa4e54eeb516fe454635a95753ddd39acda650ce703031c6973e315dd5"
dependencies = [
 "enum-ordinalize-derive",
]

[[package]]
name = "enum-ordinalize-derive"
version = "4.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d28318a75d4aead5c4db25382e8ef717932d0346600cacae6357eb5941bc5ff"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "env_logger"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd405aab171cb85d6735e5c8d9db038c17d3ca007a4d2c25f337935c3d90580"
dependencies = [
 "humantime",
 "is-terminal",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "environmental"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48c92028aaa870e83d51c64e5d4e0b6981b360c522198c23959f219a4e1b15b"

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "errno"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "976dd42dc7e85965fe702eb8164f21f450704bdde31faefd6471dba214cb594e"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3492acde4c3fc54c845eaab3eed8bd00c7a7d881f78bfc801e43a93dec1331ae"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8be9f3dfaaffdae2972880079a491a1a8bb7cbed0b8dd7a347f668b4150a3b93"
dependencies = [
 "event-listener 5.4.0",
 "pin-project-lite",
]

[[package]]
name = "exit-future"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e43f2f1833d64e33f15592464d6fdd70f349dda7b1a53088eb83cd94014008c5"
dependencies = [
 "futures",
]

[[package]]
name = "expander"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2c470c71d91ecbd179935b24170459e926382eaaa86b590b78814e180d8a8e2"
dependencies = [
 "blake2 0.10.6",
 "file-guard",
 "fs-err",
 "prettyplease",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "fallible-iterator"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4443176a9f2c162692bd3d352d745ef9413eec5782a80d8fd6f8a1ac692a07f7"

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "fatality"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec6f82451ff7f0568c6181287189126d492b5654e30a788add08027b6363d019"
dependencies = [
 "fatality-proc-macro",
 "thiserror 1.0.69",
]

[[package]]
name = "fatality-proc-macro"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb42427514b063d97ce21d5199f36c0c307d981434a6be32582bc79fe5bd2303"
dependencies = [
 "expander",
 "indexmap 2.9.0",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "fdlimit"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e182f7dbc2ef73d9ef67351c5fbbea084729c48362d3ce9dd44c28e32e277fe5"
dependencies = [
 "libc",
 "thiserror 1.0.69",
]

[[package]]
name = "ff"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0b50bfb653653f9ca9095b427bed08ab8d75a137839d9ad64eb11810d5b6393"
dependencies = [
 "rand_core 0.6.4",
 "subtle 2.6.1",
]

[[package]]
name = "fiat-crypto"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28dea519a9695b9977216879a3ebfddf92f1c08c05d984f8996aecd6ecdc811d"

[[package]]
name = "file-guard"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21ef72acf95ec3d7dbf61275be556299490a245f017cf084bd23b4f68cf9407c"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "file-per-thread-logger"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84f2e425d9790201ba4af4630191feac6dcc98765b118d4d18e91d23c2353866"
dependencies = [
 "env_logger",
 "log",
]

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "finality-grandpa"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4f8f43dc520133541781ec03a8cab158ae8b7f7169cdf22e9050aa6cf0fbdfc"
dependencies = [
 "either",
 "futures",
 "futures-timer",
 "log",
 "num-traits",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "scale-info",
]

[[package]]
name = "fixed-hash"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835c052cb0c08c1acf6ffd71c022172e18723949c8282f2b9f27efbc51e64534"
dependencies = [
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "fixedbitset"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d674e81391d1e1ab681a28d99df07927c6d4aa5b027d7da16ba32d1d21ecd99"

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "fork-tree"
version = "13.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6736bef9fd175fafbb97495565456651c43ccac2ae550faee709e11534e3621"
dependencies = [
 "parity-scale-codec",
]

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "forwarded-header-value"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8835f84f38484cc86f110a805655697908257fb9a7af005234060891557198e9"
dependencies = [
 "nonempty",
 "thiserror 1.0.69",
]

[[package]]
name = "fragile"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28dd6caf6059519a65843af8fe2a3ae298b14b80179855aeb4adc2c1934ee619"

[[package]]
name = "frame-benchmarking"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55b0892434d3cc61fab58b2e48b27b12fc162465c5af48fa283ed15bb86dbfb2"
dependencies = [
 "frame-support",
 "frame-support-procedural",
 "frame-system",
 "linregress",
 "log",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-runtime-interface",
 "sp-storage",
 "static_assertions",
]

[[package]]
name = "frame-benchmarking-cli"
version = "47.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "873cb5fd4e7f94dbb80561e29c20da73da20f74f8dc35809fbb0e2a5d35ed7b0"
dependencies = [
 "Inflector",
 "array-bytes",
 "chrono",
 "clap",
 "comfy-table",
 "cumulus-client-parachain-inherent",
 "cumulus-primitives-proof-size-hostfunction",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "gethostname",
 "handlebars",
 "itertools 0.11.0",
 "linked-hash-map",
 "log",
 "parity-scale-codec",
 "polkadot-parachain-primitives",
 "polkadot-primitives",
 "rand 0.8.5",
 "rand_pcg",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-cli",
 "sc-client-api",
 "sc-client-db",
 "sc-executor",
 "sc-runtime-utilities",
 "sc-service",
 "sc-sysinfo",
 "serde",
 "serde_json",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-core",
 "sp-database",
 "sp-externalities",
 "sp-genesis-builder",
 "sp-inherents",
 "sp-io",
 "sp-keystore",
 "sp-runtime",
 "sp-state-machine",
 "sp-storage",
 "sp-timestamp",
 "sp-transaction-pool",
 "sp-trie",
 "sp-version",
 "sp-wasm-interface",
 "subxt",
 "subxt-signer",
 "thiserror 1.0.69",
 "thousands",
]

[[package]]
name = "frame-decode"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6027a409bac4fe95b4d107f965fcdbc252fc89d884a360d076b3070b6128c094"
dependencies = [
 "frame-metadata 17.0.0",
 "parity-scale-codec",
 "scale-decode 0.14.0",
 "scale-info",
 "scale-type-resolver",
 "sp-crypto-hashing",
]

[[package]]
name = "frame-executive"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70f15cc5de17ca5665e65e8436a6faf816a2807e1bfe573fb9edcf1a81837d23"
dependencies = [
 "aquamarine",
 "frame-support",
 "frame-system",
 "frame-try-runtime",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-tracing",
]

[[package]]
name = "frame-metadata"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "701bac17e9b55e0f95067c428ebcb46496587f08e8cf4ccc0fe5903bea10dbb8"
dependencies = [
 "cfg-if",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "frame-metadata"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26de808fa6461f2485dc51811aefed108850064994fb4a62b3ac21ffa62ac8df"
dependencies = [
 "cfg-if",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "frame-metadata-hash-extension"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9cb18dcd3517d3b994f2820749fe4a9e42c2a057a8c52b30bf21b00d5d6f2b9"
dependencies = [
 "array-bytes",
 "const-hex",
 "docify",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime",
]

[[package]]
name = "frame-support"
version = "40.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6c7c272704856cc88a86aef689a778050e59f89d7ec1e4ffb3a9e8e04e6b10"
dependencies = [
 "aquamarine",
 "array-bytes",
 "binary-merkle-tree",
 "bitflags 1.3.2",
 "docify",
 "environmental",
 "frame-metadata 20.0.0",
 "frame-support-procedural",
 "impl-trait-for-tuples",
 "k256",
 "log",
 "macro_magic",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "serde_json",
 "sp-api",
 "sp-arithmetic",
 "sp-core",
 "sp-crypto-hashing-proc-macro",
 "sp-debug-derive",
 "sp-genesis-builder",
 "sp-inherents",
 "sp-io",
 "sp-metadata-ir",
 "sp-runtime",
 "sp-staking",
 "sp-state-machine",
 "sp-std",
 "sp-tracing",
 "sp-trie",
 "sp-weights",
 "tt-call",
]

[[package]]
name = "frame-support-procedural"
version = "33.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73bc18090aa96a5e69cd566fb755b5be08964b926864b2101dd900f64a870437"
dependencies = [
 "Inflector",
 "cfg-expr",
 "derive-syn-parse",
 "docify",
 "expander",
 "frame-support-procedural-tools",
 "itertools 0.11.0",
 "macro_magic",
 "proc-macro-warning",
 "proc-macro2",
 "quote",
 "sp-crypto-hashing",
 "syn 2.0.101",
]

[[package]]
name = "frame-support-procedural-tools"
version = "13.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81a088fd6fda5f53ff0c17fc7551ce8bd0ead14ba742228443c8196296a7369b"
dependencies = [
 "frame-support-procedural-tools-derive",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "frame-support-procedural-tools-derive"
version = "12.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed971c6435503a099bdac99fe4c5bea08981709e5b5a0a8535a1856f48561191"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "frame-system"
version = "40.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfc20d95c35bad22eb8b8d7ef91197a439483458237b176e621d9210f2fbff15"
dependencies = [
 "cfg-if",
 "docify",
 "frame-support",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-version",
 "sp-weights",
]

[[package]]
name = "frame-system-benchmarking"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dcf84c561e598ef31078af449398d87211867611ebc7068ba1364fba4c7e653"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "frame-system-rpc-runtime-api"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "244a5015742d349a814bc7f2aa999a9ec47924374a22672cfc3043a1eb87295f"
dependencies = [
 "docify",
 "parity-scale-codec",
 "sp-api",
]

[[package]]
name = "frame-try-runtime"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac619a778035be86fc70ac58db9ae3d5d44107dac81ddcaa2f9e8744a0c71eb1"
dependencies = [
 "frame-support",
 "parity-scale-codec",
 "sp-api",
 "sp-runtime",
]

[[package]]
name = "fs-err"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88a41f105fe1d5b6b34b2055e3dc59bb79b46b48b2040b9e6c7b4b5de097aa41"
dependencies = [
 "autocfg",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-bounded"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91f328e7fb845fc832912fb6a34f40cf6d1888c92f974d1893a54e97b5ff542e"
dependencies = [
 "futures-timer",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-lite"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5edaec856126859abb19ed65f39e90fea3a9574b9707f13539acf4abf7eb532"
dependencies = [
 "fastrand",
 "futures-core",
 "futures-io",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "futures-rustls"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f2f12607f92c69b12ed746fabf9ca4f5c482cba46679c1a75b874ed7c26adb"
dependencies = [
 "futures-io",
 "rustls",
 "rustls-pki-types",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "generic-array"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffdf9f34f1447443d37393cc6c2b8313aebddcd96906caf34e54c68d8e57d7bd"
dependencies = [
 "typenum",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "gethostname"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1ebd34e35c46e00bb73e81363248d627782724609fe1b6396f553f68fe3862e"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "getrandom"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "335ff9f135e4384c8150d6f27c6daed433577f86b4750418338c01a1a2528592"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26145e563e54f2cadc477553f1ec5ee650b00862f0a58bcd12cbdc5f0ea2d2f4"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
 "wasm-bindgen",
]

[[package]]
name = "getrandom_or_panic"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ea1015b5a70616b688dc230cfe50c8af89d972cb132d5a622814d29773b10b9"
dependencies = [
 "rand 0.8.5",
 "rand_core 0.6.4",
]

[[package]]
name = "ghash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0d8a4362ccb29cb0b265253fb0a2728f592895ee6854fd9bc13f2ffda266ff1"
dependencies = [
 "opaque-debug 0.3.1",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c80984affa11d98d1b88b66ac8853f143217b399d3c74116778ff8fdb4ed2e"
dependencies = [
 "fallible-iterator 0.2.0",
 "indexmap 1.9.3",
 "stable_deref_trait",
]

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"
dependencies = [
 "fallible-iterator 0.3.0",
 "stable_deref_trait",
]

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "governor"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68a7f542ee6b35af73b06abc0dad1c1bae89964e4e253bc4b587b91c9637867b"
dependencies = [
 "cfg-if",
 "dashmap",
 "futures",
 "futures-timer",
 "no-std-compat",
 "nonzero_ext",
 "parking_lot 0.12.3",
 "portable-atomic",
 "quanta",
 "rand 0.8.5",
 "smallvec",
 "spinning_top",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle 2.6.1",
]

[[package]]
name = "h2"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81fe527a889e1532da5c525686d96d4c2e74cdd345badf8dfef9f6b39dd5f5e8"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http 0.2.12",
 "indexmap 2.9.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "h2"
version = "0.4.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9421a676d1b147b16b82c9225157dc629087ef8ec4d5e2960f9437a90dac0a5"
dependencies = [
 "atomic-waker",
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "http 1.3.1",
 "indexmap 2.9.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "handlebars"
version = "5.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d08485b96a0e6393e9e4d1b8d48cf74ad6c063cd905eb33f42c1ce3f0377539b"
dependencies = [
 "log",
 "pest",
 "pest_derive",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "hash-db"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e7d7786361d7425ae2fe4f9e407eb0efaa0840f5212d109cc018c40c35c6ab4"

[[package]]
name = "hash256-std-hasher"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92c171d55b98633f4ed3860808f004099b36c1cc29c42cfc53aa8591b21efcf2"
dependencies = [
 "crunchy",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash",
 "allocator-api2",
 "serde",
]

[[package]]
name = "hashbrown"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84b26c544d002229e640969970a2e74021aadf6e2f96372b9c58eff97de08eb3"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "hashlink"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8094feaf31ff591f651a2664fb9cfd92bba7a60ce3197265e9482ebe753c8f7"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231dfb89cfffdbc30e7fc41579ed6066ad03abda9e567ccafae602b97ec5024"

[[package]]
name = "hermit-abi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbf6a919d6cf397374f7dfeeea91d974c7c0a7221d0d0f4f20d859d329e53fcc"

[[package]]
name = "hermit-abi"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f154ce46856750ed433c8649605bf7ed2de3bc35fd9d2a9f30cddd873c80cb08"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hex-conservative"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "212ab92002354b4819390025006c897e8140934349e8635c9b077f47b4dcbd20"

[[package]]
name = "hex-conservative"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5313b072ce3c597065a808dbf612c4c8e8590bdbf8b579508bf7a762c5eae6cd"
dependencies = [
 "arrayvec 0.7.6",
]

[[package]]
name = "hex-literal"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe2267d4ed49bc07b63801559be28c718ea06c4738b7a03c94df7386d2cde46"

[[package]]
name = "hickory-proto"
version = "0.24.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92652067c9ce6f66ce53cc38d1169daa36e6e7eb7dd3b63b5103bd9d97117248"
dependencies = [
 "async-trait",
 "cfg-if",
 "data-encoding",
 "enum-as-inner",
 "futures-channel",
 "futures-io",
 "futures-util",
 "idna",
 "ipnet",
 "once_cell",
 "rand 0.8.5",
 "socket2",
 "thiserror 1.0.69",
 "tinyvec",
 "tokio",
 "tracing",
 "url",
]

[[package]]
name = "hickory-resolver"
version = "0.24.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbb117a1ca520e111743ab2f6688eddee69db4e0ea242545a604dce8a66fd22e"
dependencies = [
 "cfg-if",
 "futures-util",
 "hickory-proto",
 "ipconfig",
 "lru-cache",
 "once_cell",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "resolv-conf",
 "smallvec",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
]

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac 0.12.1",
]

[[package]]
name = "hmac"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126888268dcc288495a26bf004b38c5fdbb31682f992c84ceb046a1f0fe38840"
dependencies = [
 "crypto-mac 0.8.0",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-drbg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ea0a1394df5b6574da6e0c1ade9e78868c9fb0a4e5ef4428e32da4676b85b1"
dependencies = [
 "digest 0.9.0",
 "generic-array 0.14.7",
 "hmac 0.8.1",
]

[[package]]
name = "http"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "601cbb57e577e2f5ef5be8e7b83f0f63994f25aa94d673e54a92d5c516d101f1"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4a85d31aea989eead29a3aaf9e1115a180df8282431156e533de47660892565"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes",
 "http 0.2.12",
 "pin-project-lite",
]

[[package]]
name = "http-body"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1efedce1fb8e6913f23e0c92de8e62cd5b772a67e7b3946df930a62566c93184"
dependencies = [
 "bytes",
 "http 1.3.1",
]

[[package]]
name = "http-body-util"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b021d93e26becf5dc7e1b75b1bed1fd93124b374ceb73f43d4d4eafec896a64a"
dependencies = [
 "bytes",
 "futures-core",
 "http 1.3.1",
 "http-body 1.0.1",
 "pin-project-lite",
]

[[package]]
name = "httparse"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dbf3de79e51f3d586ab4cb9d5c3e2c14aa28ed23d180cf89b4df0454a69cc87"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "humantime"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b112acc8b3adf4b107a8ec20977da0273a8c386765a3ec0229bd500a1443f9f"

[[package]]
name = "hyper"
version = "0.14.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41dfc780fdec9373c01bae43289ea34c972e40ee3c9f6b3c8801a35f35586ce7"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2 0.3.26",
 "http 0.2.12",
 "http-body 0.4.6",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc2b571658e38e0c01b1fdca3bbbe93c00d3d71693ff2770043f8c29bc7d6f80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "h2 0.4.10",
 "http 1.3.1",
 "http-body 1.0.1",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "smallvec",
 "tokio",
 "want",
]

[[package]]
name = "hyper-rustls"
version = "0.27.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d191583f3da1305256f22463b9bb0471acad48a4e534a5218b9963e9c1f59b2"
dependencies = [
 "futures-util",
 "http 1.3.1",
 "hyper 1.6.0",
 "hyper-util",
 "log",
 "rustls",
 "rustls-native-certs",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls",
 "tower-service",
]

[[package]]
name = "hyper-util"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "497bbc33a26fdd4af9ed9c70d63f61cf56a938375fbb32df34db9b1cd6d643f2"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "hyper 1.6.0",
 "libc",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
]

[[package]]
name = "iana-time-zone"
version = "0.1.63"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0c919e5debc312ad217002b8048a17b7d83f80703865bbfcfebb0458b0b27d8"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "log",
 "wasm-bindgen",
 "windows-core 0.61.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "icu_collections"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "200072f5d0e3614556f94a9930d5dc3e0662a652823904c3a75dc3b0af7fee47"
dependencies = [
 "displaydoc",
 "potential_utf",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locale_core"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cde2700ccaed3872079a65fb1a78f6c0a36c91570f28755dda67bc8f7d9f00a"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_normalizer"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "436880e8e18df4d7bbc06d58432329d6458cc84531f7ac5f024e93deadb37979"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00210d6893afc98edb752b664b8890f0ef174c8adbb8d0be9710fa66fbbf72d3"

[[package]]
name = "icu_properties"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2549ca8c7241c82f59c80ba2a6f415d931c5b58d24fb8412caa1a1f02c49139a"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locale_core",
 "icu_properties_data",
 "icu_provider",
 "potential_utf",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8197e866e47b68f8f7d95249e172903bec06004b18b2937f1095d40a0c57de04"

[[package]]
name = "icu_provider"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03c80da27b5f4187909049ee2d72f276f0d9f99a42c306bd0131ecfe04d8e5af"
dependencies = [
 "displaydoc",
 "icu_locale_core",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3acae9609540aa318d1bc588455225fb2085b9ed0c4f6bd0d9d5bcd86f1a0344"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "if-addrs"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cabb0019d51a643781ff15c9c8a3e5dedc365c47211270f4e8f82812fedd8f0a"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "if-watch"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cdf9d64cfcf380606e64f9a0bcf493616b65331199f984151a6fa11a7b3cde38"
dependencies = [
 "async-io",
 "core-foundation 0.9.4",
 "fnv",
 "futures",
 "if-addrs",
 "ipnet",
 "log",
 "netlink-packet-core",
 "netlink-packet-route",
 "netlink-proto",
 "netlink-sys",
 "rtnetlink",
 "system-configuration",
 "tokio",
 "windows",
]

[[package]]
name = "igd-next"
version = "0.14.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "064d90fec10d541084e7b39ead8875a5a80d9114a2b18791565253bae25f49e4"
dependencies = [
 "async-trait",
 "attohttpc",
 "bytes",
 "futures",
 "http 0.2.12",
 "hyper 0.14.32",
 "log",
 "rand 0.8.5",
 "tokio",
 "url",
 "xmltree",
]

[[package]]
name = "impl-codec"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d40b9d5e17727407e55028eafc22b2dc68781786e6d7eb8a21103f5058e3a14"
dependencies = [
 "parity-scale-codec",
]

[[package]]
name = "impl-num-traits"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "803d15461ab0dcc56706adf266158acbc44ccf719bf7d0af30705f58b90a4b8c"
dependencies = [
 "integer-sqrt",
 "num-traits",
 "uint 0.10.0",
]

[[package]]
name = "impl-serde"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a143eada6a1ec4aefa5049037a26a6d597bfd64f8c026d07b77133e02b7dd0b"
dependencies = [
 "serde",
]

[[package]]
name = "impl-trait-for-tuples"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0eb5a3343abf848c0984fe4604b2b105da9539376e24fc0a3b0007411ae4fd9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "include_dir"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "923d117408f1e49d914f1a379a309cffe4f18c05cf4e3d12e613a15fc81bd0dd"
dependencies = [
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cab85a7ed0bd5f0e76d93846e0147172bed2e2d3f859bcc33a8d9699cad1a75"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cea70ddb795996207ad57735b50c5982d8844f38ba9ee5f1aedcfb708a2aa11e"
dependencies = [
 "equivalent",
 "hashbrown 0.15.3",
]

[[package]]
name = "indexmap-nostd"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e04e2fd2b8188ea827b32ef11de88377086d690286ab35747ef7f9bf3ccb590"

[[package]]
name = "inout"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879f10e63c20629ecabbb64a8010319738c66a5cd0c29b02d63d272b03751d01"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "instant"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0242819d153cba4b4b05a5a8f2a7e9bbf97b6055b2a002b395c96b5ff3c0222"
dependencies = [
 "cfg-if",
]

[[package]]
name = "integer-sqrt"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "276ec31bcb4a9ee45f58bec6f9ec700ae4cf4f4f8f2fa7e06cb406bd5ffdd770"
dependencies = [
 "num-traits",
]

[[package]]
name = "io-lifetimes"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eae7b9aee968036d54dce06cebaefd919e4472e753296daccd6d344e3e2df0c2"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "ip_network"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2f047c0a98b2f299aa5d6d7088443570faae494e9ae1305e48be000c9e0eb1"

[[package]]
name = "ipconfig"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b58db92f96b720de98181bbbe63c831e87005ab460c1bf306eb2622b4707997f"
dependencies = [
 "socket2",
 "widestring",
 "windows-sys 0.48.0",
 "winreg",
]

[[package]]
name = "ipnet"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "469fb0b9cefa57e3ef31275ee7cacb78f2fdca44e4765491884a2b119d4eb130"

[[package]]
name = "is-terminal"
version = "0.4.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e04d7f318608d35d4b61ddd75cbdaee86b023ebe2bd5a66ee0915f0bf93095a9"
dependencies = [
 "hermit-abi 0.5.1",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5f13b858c8d314ee3e8f639011f7ccefe71f97f96e50151fb991f267928e2c"

[[package]]
name = "jni"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a87aa2bb7d2af34197c04845522473242e1aa17c12f4935d5856491a7fb8c97"
dependencies = [
 "cesu8",
 "cfg-if",
 "combine",
 "jni-sys",
 "log",
 "thiserror 1.0.69",
 "walkdir",
 "windows-sys 0.45.0",
]

[[package]]
name = "jni-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eaf4bc02d17cbdd7ff4c7438cafcdf7fb9a4613313ad11b4f8fefe7d3fa0130"

[[package]]
name = "jobserver"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f262f097c174adebe41eb73d66ae9c06b2844fb0da69969647bbddd9b0538a"
dependencies = [
 "getrandom 0.3.3",
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "jsonrpsee"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b26c20e2178756451cfeb0661fb74c47dd5988cb7e3939de7e9241fd604d42"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-proc-macros",
 "jsonrpsee-server",
 "jsonrpsee-types",
 "jsonrpsee-ws-client",
 "tokio",
 "tracing",
]

[[package]]
name = "jsonrpsee-client-transport"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bacb85abf4117092455e1573625e21b8f8ef4dec8aff13361140b2dc266cdff2"
dependencies = [
 "base64 0.22.1",
 "futures-util",
 "http 1.3.1",
 "jsonrpsee-core",
 "pin-project",
 "rustls",
 "rustls-pki-types",
 "rustls-platform-verifier",
 "soketto",
 "thiserror 1.0.69",
 "tokio",
 "tokio-rustls",
 "tokio-util",
 "tracing",
 "url",
]

[[package]]
name = "jsonrpsee-core"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "456196007ca3a14db478346f58c7238028d55ee15c1df15115596e411ff27925"
dependencies = [
 "async-trait",
 "bytes",
 "futures-timer",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "jsonrpsee-types",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "rustc-hash 2.1.1",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "jsonrpsee-proc-macros"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e65763c942dfc9358146571911b0cd1c361c2d63e2d2305622d40d36376ca80"
dependencies = [
 "heck 0.5.0",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "jsonrpsee-server"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55e363146da18e50ad2b51a0a7925fc423137a0b1371af8235b1c231a0647328"
dependencies = [
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "pin-project",
 "route-recognizer",
 "serde",
 "serde_json",
 "soketto",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower",
 "tracing",
]

[[package]]
name = "jsonrpsee-types"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08a8e70baf945b6b5752fc8eb38c918a48f1234daf11355e07106d963f860089"
dependencies = [
 "http 1.3.1",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "jsonrpsee-ws-client"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01b3323d890aa384f12148e8d2a1fd18eb66e9e7e825f9de4fa53bcc19b93eef"
dependencies = [
 "http 1.3.1",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "url",
]

[[package]]
name = "k256"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e3919bbaa2945715f0bb6d3934a173d1e9a59ac23767fbaaef277265a7411b"
dependencies = [
 "cfg-if",
 "ecdsa",
 "elliptic-curve",
 "once_cell",
 "serdect",
 "sha2 0.10.9",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "keccak-hash"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e1b8590eb6148af2ea2d75f38e7d29f5ca970d5a4df456b3ef19b8b415d0264"
dependencies = [
 "primitive-types",
 "tiny-keccak",
]

[[package]]
name = "keystream"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c33070833c9ee02266356de0c43f723152bd38bd96ddf52c82b3af10c9138b28"

[[package]]
name = "kvdb"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7d770dcb02bf6835887c3a979b5107a04ff4bbde97a5f0928d27404a155add9"
dependencies = [
 "smallvec",
]

[[package]]
name = "kvdb-memorydb"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf7a85fe66f9ff9cd74e169fdd2c94c6e1e74c412c99a73b4df3200b5d3760b2"
dependencies = [
 "kvdb",
 "parking_lot 0.12.3",
]

[[package]]
name = "kvdb-rocksdb"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b644c70b92285f66bfc2032922a79000ea30af7bc2ab31902992a5dcb9b434f6"
dependencies = [
 "kvdb",
 "num_cpus",
 "parking_lot 0.12.3",
 "regex",
 "rocksdb",
 "smallvec",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "libc"
version = "0.2.172"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d750af042f7ef4f724306de029d18836c26c1765a54a6a3f094cbd23a7267ffa"

[[package]]
name = "libloading"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a793df0d7afeac54f95b471d3af7f0d4fb975699f972341a4b76988d49cdf0c"
dependencies = [
 "cfg-if",
 "windows-targets 0.53.0",
]

[[package]]
name = "libm"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9fbbcab51052fe104eb5e5d351cf728d30a5be1fe14d9be8a3b097481fb97de"

[[package]]
name = "libp2p"
version = "0.54.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbbe80f9c7e00526cd6b838075b9c171919404a4732cb2fa8ece0a093223bfc4"
dependencies = [
 "bytes",
 "either",
 "futures",
 "futures-timer",
 "getrandom 0.2.16",
 "libp2p-allow-block-list",
 "libp2p-connection-limits",
 "libp2p-core",
 "libp2p-dns",
 "libp2p-identify",
 "libp2p-identity",
 "libp2p-kad",
 "libp2p-mdns",
 "libp2p-metrics",
 "libp2p-noise",
 "libp2p-ping",
 "libp2p-quic",
 "libp2p-request-response",
 "libp2p-swarm",
 "libp2p-tcp",
 "libp2p-upnp",
 "libp2p-websocket",
 "libp2p-yamux",
 "multiaddr 0.18.2",
 "pin-project",
 "rw-stream-sink",
 "thiserror 1.0.69",
]

[[package]]
name = "libp2p-allow-block-list"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1027ccf8d70320ed77e984f273bc8ce952f623762cb9bf2d126df73caef8041"
dependencies = [
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "void",
]

[[package]]
name = "libp2p-connection-limits"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d003540ee8baef0d254f7b6bfd79bac3ddf774662ca0abf69186d517ef82ad8"
dependencies = [
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "void",
]

[[package]]
name = "libp2p-core"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a61f26c83ed111104cd820fe9bc3aaabbac5f1652a1d213ed6e900b7918a1298"
dependencies = [
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "libp2p-identity",
 "multiaddr 0.18.2",
 "multihash 0.19.3",
 "multistream-select",
 "once_cell",
 "parking_lot 0.12.3",
 "pin-project",
 "quick-protobuf",
 "rand 0.8.5",
 "rw-stream-sink",
 "smallvec",
 "thiserror 1.0.69",
 "tracing",
 "unsigned-varint 0.8.0",
 "void",
 "web-time",
]

[[package]]
name = "libp2p-dns"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97f37f30d5c7275db282ecd86e54f29dd2176bd3ac656f06abf43bedb21eb8bd"
dependencies = [
 "async-trait",
 "futures",
 "hickory-resolver",
 "libp2p-core",
 "libp2p-identity",
 "parking_lot 0.12.3",
 "smallvec",
 "tracing",
]

[[package]]
name = "libp2p-identify"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1711b004a273be4f30202778856368683bd9a83c4c7dcc8f848847606831a4e3"
dependencies = [
 "asynchronous-codec 0.7.0",
 "either",
 "futures",
 "futures-bounded",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "lru",
 "quick-protobuf",
 "quick-protobuf-codec",
 "smallvec",
 "thiserror 1.0.69",
 "tracing",
 "void",
]

[[package]]
name = "libp2p-identity"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbb68ea10844211a59ce46230909fd0ea040e8a192454d4cc2ee0d53e12280eb"
dependencies = [
 "bs58",
 "ed25519-dalek",
 "hkdf",
 "multihash 0.19.3",
 "quick-protobuf",
 "rand 0.8.5",
 "sha2 0.10.9",
 "thiserror 2.0.12",
 "tracing",
 "zeroize",
]

[[package]]
name = "libp2p-kad"
version = "0.46.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced237d0bd84bbebb7c2cad4c073160dacb4fe40534963c32ed6d4c6bb7702a3"
dependencies = [
 "arrayvec 0.7.6",
 "asynchronous-codec 0.7.0",
 "bytes",
 "either",
 "fnv",
 "futures",
 "futures-bounded",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "quick-protobuf",
 "quick-protobuf-codec",
 "rand 0.8.5",
 "sha2 0.10.9",
 "smallvec",
 "thiserror 1.0.69",
 "tracing",
 "uint 0.9.5",
 "void",
 "web-time",
]

[[package]]
name = "libp2p-mdns"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14b8546b6644032565eb29046b42744aee1e9f261ed99671b2c93fb140dba417"
dependencies = [
 "data-encoding",
 "futures",
 "hickory-proto",
 "if-watch",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "rand 0.8.5",
 "smallvec",
 "socket2",
 "tokio",
 "tracing",
 "void",
]

[[package]]
name = "libp2p-metrics"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77ebafa94a717c8442d8db8d3ae5d1c6a15e30f2d347e0cd31d057ca72e42566"
dependencies = [
 "futures",
 "libp2p-core",
 "libp2p-identify",
 "libp2p-identity",
 "libp2p-kad",
 "libp2p-ping",
 "libp2p-swarm",
 "pin-project",
 "prometheus-client",
 "web-time",
]

[[package]]
name = "libp2p-noise"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36b137cb1ae86ee39f8e5d6245a296518912014eaa87427d24e6ff58cfc1b28c"
dependencies = [
 "asynchronous-codec 0.7.0",
 "bytes",
 "curve25519-dalek",
 "futures",
 "libp2p-core",
 "libp2p-identity",
 "multiaddr 0.18.2",
 "multihash 0.19.3",
 "once_cell",
 "quick-protobuf",
 "rand 0.8.5",
 "sha2 0.10.9",
 "snow",
 "static_assertions",
 "thiserror 1.0.69",
 "tracing",
 "x25519-dalek",
 "zeroize",
]

[[package]]
name = "libp2p-ping"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "005a34420359223b974ee344457095f027e51346e992d1e0dcd35173f4cdd422"
dependencies = [
 "either",
 "futures",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "rand 0.8.5",
 "tracing",
 "void",
 "web-time",
]

[[package]]
name = "libp2p-quic"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46352ac5cd040c70e88e7ff8257a2ae2f891a4076abad2c439584a31c15fd24e"
dependencies = [
 "bytes",
 "futures",
 "futures-timer",
 "if-watch",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-tls",
 "parking_lot 0.12.3",
 "quinn",
 "rand 0.8.5",
 "ring 0.17.14",
 "rustls",
 "socket2",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
]

[[package]]
name = "libp2p-request-response"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1356c9e376a94a75ae830c42cdaea3d4fe1290ba409a22c809033d1b7dcab0a6"
dependencies = [
 "async-trait",
 "futures",
 "futures-bounded",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "rand 0.8.5",
 "smallvec",
 "tracing",
 "void",
 "web-time",
]

[[package]]
name = "libp2p-swarm"
version = "0.45.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7dd6741793d2c1fb2088f67f82cf07261f25272ebe3c0b0c311e0c6b50e851a"
dependencies = [
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm-derive",
 "lru",
 "multistream-select",
 "once_cell",
 "rand 0.8.5",
 "smallvec",
 "tokio",
 "tracing",
 "void",
 "web-time",
]

[[package]]
name = "libp2p-swarm-derive"
version = "0.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "206e0aa0ebe004d778d79fb0966aa0de996c19894e2c0605ba2f8524dd4443d8"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "libp2p-tcp"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad964f312c59dcfcac840acd8c555de8403e295d39edf96f5240048b5fcaa314"
dependencies = [
 "futures",
 "futures-timer",
 "if-watch",
 "libc",
 "libp2p-core",
 "libp2p-identity",
 "socket2",
 "tokio",
 "tracing",
]

[[package]]
name = "libp2p-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47b23dddc2b9c355f73c1e36eb0c3ae86f7dc964a3715f0731cfad352db4d847"
dependencies = [
 "futures",
 "futures-rustls",
 "libp2p-core",
 "libp2p-identity",
 "rcgen",
 "ring 0.17.14",
 "rustls",
 "rustls-webpki 0.101.7",
 "thiserror 1.0.69",
 "x509-parser 0.16.0",
 "yasna",
]

[[package]]
name = "libp2p-upnp"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01bf2d1b772bd3abca049214a3304615e6a36fa6ffc742bdd1ba774486200b8f"
dependencies = [
 "futures",
 "futures-timer",
 "igd-next",
 "libp2p-core",
 "libp2p-swarm",
 "tokio",
 "tracing",
 "void",
]

[[package]]
name = "libp2p-websocket"
version = "0.44.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "888b2ff2e5d8dcef97283daab35ad1043d18952b65e05279eecbe02af4c6e347"
dependencies = [
 "either",
 "futures",
 "futures-rustls",
 "libp2p-core",
 "libp2p-identity",
 "parking_lot 0.12.3",
 "pin-project-lite",
 "rw-stream-sink",
 "soketto",
 "thiserror 1.0.69",
 "tracing",
 "url",
 "webpki-roots",
]

[[package]]
name = "libp2p-yamux"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "788b61c80789dba9760d8c669a5bedb642c8267555c803fabd8396e4ca5c5882"
dependencies = [
 "either",
 "futures",
 "libp2p-core",
 "thiserror 1.0.69",
 "tracing",
 "yamux 0.12.1",
 "yamux 0.13.4",
]

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.9.0",
 "libc",
 "redox_syscall 0.5.12",
]

[[package]]
name = "librocksdb-sys"
version = "0.11.0+8.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3386f101bcb4bd252d8e9d2fb41ec3b0862a15a62b478c355b2982efa469e3e"
dependencies = [
 "bindgen",
 "bzip2-sys",
 "cc",
 "glob",
 "libc",
 "libz-sys",
 "tikv-jemalloc-sys",
]

[[package]]
name = "libsecp256k1"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79019718125edc905a079a70cfa5f3820bc76139fc91d6f9abc27ea2a887139"
dependencies = [
 "arrayref",
 "base64 0.22.1",
 "digest 0.9.0",
 "hmac-drbg",
 "libsecp256k1-core",
 "libsecp256k1-gen-ecmult",
 "libsecp256k1-gen-genmult",
 "rand 0.8.5",
 "serde",
 "sha2 0.9.9",
 "typenum",
]

[[package]]
name = "libsecp256k1-core"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be9b9bb642d8522a44d533eab56c16c738301965504753b03ad1de3425d5451"
dependencies = [
 "crunchy",
 "digest 0.9.0",
 "subtle 2.6.1",
]

[[package]]
name = "libsecp256k1-gen-ecmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3038c808c55c87e8a172643a7d87187fc6c4174468159cb3090659d55bcb4809"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsecp256k1-gen-genmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db8d6ba2cec9eacc40e6e8ccc98931840301f1006e95647ceb2dd5c3aa06f7c"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libz-sys"
version = "1.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b70e7a7df205e92a1a4cd9aaae7898dac0aa555503cc0a649494d0d60e7651d"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "link-cplusplus"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a6f6da007f968f9def0d65a05b187e2960183de70c160204ecfccf0ee330212"
dependencies = [
 "cc",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linked_hash_set"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bae85b5be22d9843c80e5fc80e9b64c8a3b1f98f867c709956eca3efff4e92e2"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "linregress"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9eda9dcf4f2a99787827661f312ac3219292549c2ee992bf9a6248ffb066bf7"
dependencies = [
 "nalgebra",
]

[[package]]
name = "linux-raw-sys"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f051f77a7c8e6957c0696eac88f26b0117e54f52d3fc682ab19397a8812846a4"

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd945864f07fe9f5371a27ad7b52a172b4b499999f1d97574c9fa68373937e12"

[[package]]
name = "lioness"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ae926706ba42c425c9457121178330d75e273df2e82e28b758faf3de3a9acb9"
dependencies = [
 "arrayref",
 "blake2 0.8.1",
 "chacha",
 "keystream",
]

[[package]]
name = "litemap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "241eaef5fd12c88705a01fc1066c48c4b36e0dd4377dcdc7ec3942cea7a69956"

[[package]]
name = "litep2p"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71056c23c896bb0e18113b2d2f1989be95135e6bdeedb0b757422ee21a073eb"
dependencies = [
 "async-trait",
 "bs58",
 "bytes",
 "cid 0.11.1",
 "ed25519-dalek",
 "futures",
 "futures-timer",
 "hickory-resolver",
 "indexmap 2.9.0",
 "libc",
 "mockall",
 "multiaddr 0.17.1",
 "multihash 0.17.0",
 "network-interface",
 "parking_lot 0.12.3",
 "pin-project",
 "prost 0.13.5",
 "prost-build",
 "rand 0.8.5",
 "serde",
 "sha2 0.10.9",
 "simple-dns",
 "smallvec",
 "snow",
 "socket2",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tokio-tungstenite",
 "tokio-util",
 "tracing",
 "uint 0.10.0",
 "unsigned-varint 0.8.0",
 "url",
 "x25519-dalek",
 "x509-parser 0.17.0",
 "yamux 0.13.4",
 "yasna",
 "zeroize",
]

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "lru-cache"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31e24f1ad8321ca0e8a1e0ac13f23cb668e6f5466c2c57319f6a5cf1cc8e3b1c"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "lru-slab"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "112b39cec0b298b6c1999fee3e31427f74f676e4cb9879ed1a121b43661a4154"

[[package]]
name = "lz4"
version = "1.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a20b523e860d03443e98350ceaac5e71c6ba89aea7d960769ec3ce37f4de5af4"
dependencies = [
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.11.1+lz4-1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bd8c0d6c6ed0cd30b3652886bb8711dc4bb01d637a68105a3d5158039b418e6"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc",
]

[[package]]
name = "macro_magic"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc33f9f0351468d26fbc53d9ce00a096c8522ecb42f19b50f34f2c422f76d21d"
dependencies = [
 "macro_magic_core",
 "macro_magic_macros",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "macro_magic_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1687dc887e42f352865a393acae7cf79d98fab6351cde1f58e9e057da89bf150"
dependencies = [
 "const-random",
 "derive-syn-parse",
 "macro_magic_core_macros",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "macro_magic_core_macros"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b02abfe41815b5bd98dbd4260173db2c116dda171dc0fe7838cb206333b83308"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "macro_magic_macros"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73ea28ee64b88876bf45277ed9a5817c1817df061a74f2b988971a12570e5869"
dependencies = [
 "macro_magic_core",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matrixmultiply"
version = "0.3.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06de3016e9fae57a36fd14dba131fccf49f74b40b7fbdb472f96e361ec71a08"
dependencies = [
 "autocfg",
 "rawpointer",
]

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memfd"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2cffa4ad52c6f791f4f8b15f0c05f9824b2ced1160e88cc393d64fff9a8ac64"
dependencies = [
 "rustix 0.38.44",
]

[[package]]
name = "memmap2"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83faa42c0a078c393f6b29d5db232d8be22776a891f8f56e5284faee4a20b327"
dependencies = [
 "libc",
]

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d61c719bcfbcf5d62b3a09efa6088de8c54bc0bfcd3ea7ae39fcc186108b8de1"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory-db"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "808b50db46293432a45e63bc15ea51e0ab4c0a1647b8eb114e31a3e698dd6fbe"
dependencies = [
 "hash-db",
]

[[package]]
name = "merkleized-metadata"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc9b7ac0ce054412d9a85ff39bac27aec27483b06cef8756b57d9c29d448d081"
dependencies = [
 "array-bytes",
 "blake3",
 "frame-metadata 20.0.0",
 "parity-scale-codec",
 "scale-decode 0.13.1",
 "scale-info",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.8.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be647b768db090acb35d5ec5db2b0e1f1de11133ca123b9eacf5137868f892a"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "mixnet"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daa3eb39495d8e2e2947a1d862852c90cc6a4a8845f8b41c8829cb9fcc047f4a"
dependencies = [
 "arrayref",
 "arrayvec 0.7.6",
 "bitflags 1.3.2",
 "blake2 0.10.6",
 "c2-chacha",
 "curve25519-dalek",
 "either",
 "hashlink",
 "lioness",
 "log",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_distr",
 "subtle 2.6.1",
 "thiserror 1.0.69",
 "zeroize",
]

[[package]]
name = "mockall"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39a6bfcc6c8c7eed5ee98b9c3e33adc726054389233e201c95dab2d41a3839d2"
dependencies = [
 "cfg-if",
 "downcast",
 "fragile",
 "mockall_derive",
 "predicates",
 "predicates-tree",
]

[[package]]
name = "mockall_derive"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25ca3004c2efe9011bd4e461bd8256445052b9615405b4f7ea43fc8ca5c20898"
dependencies = [
 "cfg-if",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "multi-stash"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "685a9ac4b61f4e728e1d2c6a7844609c16527aeb5e6c865915c08e619c16410f"

[[package]]
name = "multiaddr"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b36f567c7099511fa8612bbbb52dda2419ce0bdbacf31714e3a5ffdb766d3bd"
dependencies = [
 "arrayref",
 "byteorder",
 "data-encoding",
 "log",
 "multibase",
 "multihash 0.17.0",
 "percent-encoding",
 "serde",
 "static_assertions",
 "unsigned-varint 0.7.2",
 "url",
]

[[package]]
name = "multiaddr"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe6351f60b488e04c1d21bc69e56b89cb3f5e8f5d22557d6e8031bdfd79b6961"
dependencies = [
 "arrayref",
 "byteorder",
 "data-encoding",
 "libp2p-identity",
 "multibase",
 "multihash 0.19.3",
 "percent-encoding",
 "serde",
 "static_assertions",
 "unsigned-varint 0.8.0",
 "url",
]

[[package]]
name = "multibase"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b3539ec3c1f04ac9748a260728e855f261b4977f5c3406612c884564f329404"
dependencies = [
 "base-x",
 "data-encoding",
 "data-encoding-macro",
]

[[package]]
name = "multihash"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835d6ff01d610179fbce3de1694d007e500bf33a7f29689838941d6bf783ae40"
dependencies = [
 "blake2b_simd",
 "blake2s_simd",
 "blake3",
 "core2",
 "digest 0.10.7",
 "multihash-derive",
 "sha2 0.10.9",
 "sha3",
 "unsigned-varint 0.7.2",
]

[[package]]
name = "multihash"
version = "0.19.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b430e7953c29dd6a09afc29ff0bb69c6e306329ee6794700aee27b76a1aea8d"
dependencies = [
 "core2",
 "unsigned-varint 0.8.0",
]

[[package]]
name = "multihash-derive"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6d4752e6230d8ef7adf7bd5d8c4b1f6561c1014c5ba9a37445ccefe18aa1db"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "synstructure 0.12.6",
]

[[package]]
name = "multimap"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d87ecb2933e8aeadb3e3a02b828fed80a7528047e68b4f424523a0981a3a084"

[[package]]
name = "multistream-select"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea0df8e5eec2298a62b326ee4f0d7fe1a6b90a09dfcf9df37b38f947a8c42f19"
dependencies = [
 "bytes",
 "futures",
 "log",
 "pin-project",
 "smallvec",
 "unsigned-varint 0.7.2",
]

[[package]]
name = "nalgebra"
version = "0.33.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26aecdf64b707efd1310e3544d709c5c0ac61c13756046aaaba41be5c4f66a3b"
dependencies = [
 "approx",
 "matrixmultiply",
 "num-complex",
 "num-rational",
 "num-traits",
 "simba",
 "typenum",
]

[[package]]
name = "names"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bddcd3bf5144b6392de80e04c347cd7fab2508f6df16a85fc496ecd5cec39bc"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "nanorand"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a51313c5820b0b02bd422f4b44776fbf47961755c74ce64afc73bfad10226c3"

[[package]]
name = "netlink-packet-core"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72724faf704479d67b388da142b186f916188505e7e0b26719019c525882eda4"
dependencies = [
 "anyhow",
 "byteorder",
 "netlink-packet-utils",
]

[[package]]
name = "netlink-packet-route"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "053998cea5a306971f88580d0829e90f270f940befd7cf928da179d4187a5a66"
dependencies = [
 "anyhow",
 "bitflags 1.3.2",
 "byteorder",
 "libc",
 "netlink-packet-core",
 "netlink-packet-utils",
]

[[package]]
name = "netlink-packet-utils"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ede8a08c71ad5a95cdd0e4e52facd37190977039a4704eb82a283f713747d34"
dependencies = [
 "anyhow",
 "byteorder",
 "paste",
 "thiserror 1.0.69",
]

[[package]]
name = "netlink-proto"
version = "0.11.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72452e012c2f8d612410d89eea01e2d9b56205274abb35d53f60200b2ec41d60"
dependencies = [
 "bytes",
 "futures",
 "log",
 "netlink-packet-core",
 "netlink-sys",
 "thiserror 2.0.12",
]

[[package]]
name = "netlink-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16c903aa70590cb93691bf97a767c8d1d6122d2cc9070433deb3bbf36ce8bd23"
dependencies = [
 "bytes",
 "futures",
 "libc",
 "log",
 "tokio",
]

[[package]]
name = "network-interface"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3329f515506e4a2de3aa6e07027a6758e22e0f0e8eaf64fa47261cec2282602"
dependencies = [
 "cc",
 "libc",
 "thiserror 1.0.69",
 "winapi",
]

[[package]]
name = "nix"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "598beaf3cc6fdd9a5dfb1630c2800c7acd31df7aaf0f565796fba2b53ca1af1b"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if",
 "libc",
]

[[package]]
name = "no-std-compat"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b93853da6d84c2e3c7d730d6473e8817692dd89be387eb01b94d7f108ecb5b8c"

[[package]]
name = "nodrop"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72ef4a56884ca558e5ddb05a1d1e7e1bfd9a68d9ed024c21704cc98872dae1bb"

[[package]]
name = "nohash-hasher"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bf50223579dc7cdcfb3bfcacf7069ff68243f8c363f62ffa99cf000a6b9c451"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nonempty"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e591e719385e6ebaeb5ce5d3887f7d5676fceca6411d1925ccc95745f3d6f7"

[[package]]
name = "nonzero_ext"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38bf9645c8b145698bb0b18a4637dcacbc421ea49bef2317e4fd8065a387cf21"

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "num-bigint"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5e44f723f1133c9deac646763579fdb3ac745e418f2a7af9cd0c431da1f20b9"
dependencies = [
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f88a1307638156682bada9d7604135552957b7818057dcef22705b4d509495"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec 0.7.6",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f83d14da390562dca69fc84082e73e548e1ad308d24accdedd2720017cb37824"
dependencies = [
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
]

[[package]]
name = "object"
version = "0.30.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03b4680b86d9cfafba8fc491dc9b6df26b68cf40e9e6cd73909194759a63c385"
dependencies = [
 "crc32fast",
 "hashbrown 0.13.2",
 "indexmap 1.9.3",
 "memchr",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "memchr",
]

[[package]]
name = "oid-registry"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d8034d9489cdaf79228eb9f6a3b8d7bb32ba00d6645ebd48eef4077ceb5bd9"
dependencies = [
 "asn1-rs 0.6.2",
]

[[package]]
name = "oid-registry"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12f40cff3dde1b6087cc5d5f5d4d65712f34016a03ed60e9c08dcc392736b5b7"
dependencies = [
 "asn1-rs 0.7.1",
]

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"

[[package]]
name = "opaque-debug"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2839e79665f131bdb5782e51f2c6c9599c133c6098982a54c794358bf432529c"

[[package]]
name = "opaque-debug"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08d65885ee38876c4f86fa503fb49d7b507c2b62552df7c70b2fce627e06381"

[[package]]
name = "openssl-probe"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d05e27ee213611ffe7d6348b942e8f942b37114c00cc03cec254295a4a17852e"

[[package]]
name = "option-ext"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04744f49eae99ab78e0d5c0b603ab218f515ea8cfe5a456d7629ad883a3b6e7d"

[[package]]
name = "orchestra"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41f6bbacc8c189a3f2e45e0fd0436e5d97f194db888e721bdbc3973e7dbed4c2"
dependencies = [
 "async-trait",
 "dyn-clonable",
 "futures",
 "futures-timer",
 "orchestra-proc-macro",
 "pin-project",
 "prioritized-metered-channel",
 "thiserror 1.0.69",
 "tracing",
]

[[package]]
name = "orchestra-proc-macro"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7b1d40dd8f367db3c65bec8d3dd47d4a604ee8874480738f93191bddab4e0e0"
dependencies = [
 "expander",
 "indexmap 2.9.0",
 "itertools 0.11.0",
 "petgraph 0.6.5",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "pallet-aura"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4afcad52b78910d4acb9b260758f69d6167c2e5e03040bd87f42fa2e182f9bad"
dependencies = [
 "frame-support",
 "frame-system",
 "log",
 "pallet-timestamp",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto",
 "sp-consensus-aura",
 "sp-runtime",
]

[[package]]
name = "pallet-authorship"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d08ec7786d0232e2f92f36e9e20c7414f3b4d763a35569c0b9c32ed90ed62c50"
dependencies = [
 "frame-support",
 "frame-system",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime",
]

[[package]]
name = "pallet-balances"
version = "41.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4dcd7bf033312c976e0c044a80b4cd8b88471d7371baae6fea67b3f42eba288b"
dependencies = [
 "docify",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "pallet-grandpa"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7248e836db9e07b2262b83bd638e0070f5d2357d63519920317473ad90d3fac2"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "pallet-authorship",
 "pallet-session",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-session",
 "sp-staking",
]

[[package]]
name = "pallet-session"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "957973f62a34695f5b6e17b33ad67a11c8310fe9e16c898769c047add6b34c22"
dependencies = [
 "frame-support",
 "frame-system",
 "impl-trait-for-tuples",
 "log",
 "pallet-timestamp",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-session",
 "sp-staking",
 "sp-state-machine",
 "sp-trie",
]

[[package]]
name = "pallet-sudo"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdcb93e724a2acc7041d1e368895bc3ce272b6db8338a079037395cd5e6a97db"
dependencies = [
 "docify",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec",
 "scale-info",
 "sp-io",
 "sp-runtime",
]

[[package]]
name = "pallet-template"
version = "0.1.0"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
]

[[package]]
name = "pallet-timestamp"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccf2c41020fe6b676345a2f4e224faf128ba26dfc5d4da7938d1a91049dc3203"
dependencies = [
 "docify",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-inherents",
 "sp-io",
 "sp-runtime",
 "sp-storage",
 "sp-timestamp",
]

[[package]]
name = "pallet-transaction-payment"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8ebd61b64848e39e5615832c964dc10b63bcebff26a9ec1cb867b4087240a03"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
]

[[package]]
name = "pallet-transaction-payment-rpc"
version = "43.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2d27cee9496b7e9d6ad6ae4ff6daa71d47f98fd2593fd16e79537f5993c1447"
dependencies = [
 "jsonrpsee",
 "pallet-transaction-payment-rpc-runtime-api",
 "parity-scale-codec",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-weights",
]

[[package]]
name = "pallet-transaction-payment-rpc-runtime-api"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bd3329d44b44623b7615cc069b292f2a1fe5c0f4a6625c36cc906f2a43fcc1"
dependencies = [
 "pallet-transaction-payment",
 "parity-scale-codec",
 "sp-api",
 "sp-runtime",
 "sp-weights",
]

[[package]]
name = "parity-bip39"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e69bf016dc406eff7d53a7d3f7cf1c2e72c82b9088aac1118591e36dd2cd3e9"
dependencies = [
 "bitcoin_hashes 0.13.0",
 "rand 0.8.5",
 "rand_core 0.6.4",
 "serde",
 "unicode-normalization",
]

[[package]]
name = "parity-db"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "592a28a24b09c9dc20ac8afaa6839abc417c720afe42c12e1e4a9d6aa2508d2e"
dependencies = [
 "blake2 0.10.6",
 "crc32fast",
 "fs2",
 "hex",
 "libc",
 "log",
 "lz4",
 "memmap2 0.5.10",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "siphasher 0.3.11",
 "snap",
 "winapi",
]

[[package]]
name = "parity-scale-codec"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9fde3d0718baf5bc92f577d652001da0f8d54cd03a7974e118d04fc888dc23d"
dependencies = [
 "arrayvec 0.7.6",
 "bitvec",
 "byte-slice-cast",
 "bytes",
 "const_format",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive",
 "rustversion",
 "serde",
]

[[package]]
name = "parity-scale-codec-derive"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581c837bb6b9541ce7faa9377c20616e4fb7650f6b0f68bc93c827ee504fb7b3"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "parity-wasm"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1ad0aff30c1da14b1254fcb2af73e1fa9a28670e584a626f53a369d0e157304"

[[package]]
name = "parking"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f38d5652c16fde515bb1ecef450ab0f6a219d619a7274976324d5e377f7dceba"

[[package]]
name = "parking_lot"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.6",
]

[[package]]
name = "parking_lot"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bf18183cf54e8d6059647fc3063646a1801cf30896933ec2311622cc4b9a27"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "parking_lot_core"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc"
dependencies = [
 "cfg-if",
 "instant",
 "libc",
 "redox_syscall 0.2.16",
 "smallvec",
 "winapi",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.5.12",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "partial_sort"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7924d1d0ad836f665c9065e26d016c673ece3993f30d340068b16f282afc1156"

[[package]]
name = "password-hash"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "346f04948ba92c43e8469c1ee6736c7563d71012b17d40745260fe106aac2166"
dependencies = [
 "base64ct",
 "rand_core 0.6.4",
 "subtle 2.6.1",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pbkdf2"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ed6a7761f76e3b9f92dfb0a60a6a6477c61024b775147ff0973a02653abaf2"
dependencies = [
 "digest 0.10.7",
 "hmac 0.12.1",
 "password-hash",
]

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "pem"
version = "3.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38af38e8470ac9dee3ce1bae1af9c1671fffc44ddfd8bd1d0a3445bf349a8ef3"
dependencies = [
 "base64 0.22.1",
 "serde",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "pest"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "198db74531d58c70a361c42201efde7e2591e976d518caf7662a47dc5720e7b6"
dependencies = [
 "memchr",
 "thiserror 2.0.12",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d725d9cfd79e87dccc9341a2ef39d1b6f6353d68c4b33c177febbe1a402c97c5"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db7d01726be8ab66ab32f9df467ae8b1148906685bbe75c82d1e65d7f5b3f841"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pest_meta"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f9f832470494906d1fca5329f8ab5791cc60beb230c74815dff541cbd2b5ca0"
dependencies = [
 "once_cell",
 "pest",
 "sha2 0.10.9",
]

[[package]]
name = "petgraph"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4c5cc86750666a3ed20bdaf5ca2a0344f9c67674cae0515bec2da16fbaa47db"
dependencies = [
 "fixedbitset 0.4.2",
 "indexmap 2.9.0",
]

[[package]]
name = "petgraph"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3672b37090dbd86368a4145bc067582552b29c27377cad4e0a306c97f9bd7772"
dependencies = [
 "fixedbitset 0.5.7",
 "indexmap 2.9.0",
]

[[package]]
name = "pin-project"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677f1add503faace112b9f1373e43e9e054bfdd22ff1a63c1bc485eaec6a6a8a"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e918e4ff8c4549eb882f14b3a4bc8c8bc93de829416eacf579f1207a8fbf861"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "piper"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96c8c490f422ef9a4efd2cb5b42b76c8613d7e7dfc1caf667b8a3350a5acc066"
dependencies = [
 "atomic-waker",
 "fastrand",
 "futures-io",
]

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der",
 "spki",
]

[[package]]
name = "pkg-config"
version = "0.3.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7edddbd0b52d732b21ad9a5fab5c704c14cd949e5e9a1ec5929a24fded1b904c"

[[package]]
name = "polkadot-core-primitives"
version = "17.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7c519ee804fd08d7464871bd2fe164e8f0683501ea59d2a10f5ef214dacb3b"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "polkadot-node-metrics"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf32dc99967ac877ee66e1c2daa786c67d4ef6cb4509e5a14f3761872796a7e7"
dependencies = [
 "bs58",
 "futures",
 "futures-timer",
 "parity-scale-codec",
 "polkadot-primitives",
 "prioritized-metered-channel",
 "sc-cli",
 "sc-service",
 "sc-tracing",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "polkadot-node-network-protocol"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3821cb26256e3ee8af41f156198b075c030d4d066fcfea237b5596a154a1bf8"
dependencies = [
 "async-channel 1.9.0",
 "async-trait",
 "bitvec",
 "derive_more 0.99.20",
 "fatality",
 "futures",
 "hex",
 "parity-scale-codec",
 "polkadot-node-primitives",
 "polkadot-primitives",
 "rand 0.8.5",
 "sc-authority-discovery",
 "sc-network",
 "sc-network-types",
 "sp-runtime",
 "strum 0.26.3",
 "thiserror 1.0.69",
 "tracing-gum",
]

[[package]]
name = "polkadot-node-primitives"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "758d25d7532f3a952f4a52079e580e4fc45a9825ac926cbfac6128d8236b8260"
dependencies = [
 "bitvec",
 "bounded-vec",
 "futures",
 "futures-timer",
 "parity-scale-codec",
 "polkadot-parachain-primitives",
 "polkadot-primitives",
 "sc-keystore",
 "schnorrkel",
 "serde",
 "sp-application-crypto",
 "sp-consensus-babe",
 "sp-consensus-slots",
 "sp-keystore",
 "sp-maybe-compressed-blob",
 "thiserror 1.0.69",
 "zstd 0.12.4",
]

[[package]]
name = "polkadot-node-subsystem-types"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c49e4a49a9be59cdd68922591e706bd6093d5175277b8417b37982025887a7af"
dependencies = [
 "async-trait",
 "derive_more 0.99.20",
 "fatality",
 "futures",
 "orchestra",
 "polkadot-node-network-protocol",
 "polkadot-node-primitives",
 "polkadot-primitives",
 "polkadot-statement-table",
 "sc-client-api",
 "sc-network",
 "sc-network-types",
 "sc-transaction-pool-api",
 "smallvec",
 "sp-api",
 "sp-authority-discovery",
 "sp-blockchain",
 "sp-consensus-babe",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
]

[[package]]
name = "polkadot-overseer"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "259500517ee35d71f64f1d03dfc62684105fe3568372f6ff08a20349db250c38"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "orchestra",
 "polkadot-node-metrics",
 "polkadot-node-network-protocol",
 "polkadot-node-primitives",
 "polkadot-node-subsystem-types",
 "polkadot-primitives",
 "sc-client-api",
 "sp-core",
 "tikv-jemalloc-ctl",
 "tracing-gum",
]

[[package]]
name = "polkadot-parachain-primitives"
version = "16.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72943c0948c686b47bacb1a03e59baff63bfba2e16e208d77f0f8615827f8564"
dependencies = [
 "bounded-collections",
 "derive_more 0.99.20",
 "parity-scale-codec",
 "polkadot-core-primitives",
 "scale-info",
 "serde",
 "sp-core",
 "sp-runtime",
 "sp-weights",
]

[[package]]
name = "polkadot-primitives"
version = "18.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d46b3d45e295d975a9be6128212b29e0efd05f26cdde4a45115424a1f6bad0dd"
dependencies = [
 "bitvec",
 "hex-literal",
 "log",
 "parity-scale-codec",
 "polkadot-core-primitives",
 "polkadot-parachain-primitives",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-arithmetic",
 "sp-authority-discovery",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-io",
 "sp-keystore",
 "sp-runtime",
 "sp-staking",
 "sp-std",
 "thiserror 1.0.69",
]

[[package]]
name = "polkadot-sdk"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb819108697967452fa6d8d96ab4c0d48cbaa423b3156499dcb24f1cf95d6775"
dependencies = [
 "sp-crypto-hashing",
]

[[package]]
name = "polkadot-statement-table"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea6d47fecf55aba37980922e8eff6d13667ca20ac1969637c220770a033d81f1"
dependencies = [
 "parity-scale-codec",
 "polkadot-primitives",
 "tracing-gum",
]

[[package]]
name = "polkavm"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd044ab1d3b11567ab6b98ca71259a992b4034220d5972988a0e96518e5d343d"
dependencies = [
 "libc",
 "log",
 "polkavm-assembler",
 "polkavm-common",
 "polkavm-linux-raw",
]

[[package]]
name = "polkavm-assembler"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaad38dc420bfed79e6f731471c973ce5ff5e47ab403e63cf40358fef8a6368f"
dependencies = [
 "log",
]

[[package]]
name = "polkavm-common"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31ff33982a807d8567645d4784b9b5d7ab87bcb494f534a57cadd9012688e102"
dependencies = [
 "log",
 "polkavm-assembler",
]

[[package]]
name = "polkavm-derive"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2eb703f3b6404c13228402e98a5eae063fd16b8f58afe334073ec105ee4117e"
dependencies = [
 "polkavm-derive-impl-macro",
]

[[package]]
name = "polkavm-derive-impl"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f2116a92e6e96220a398930f4c8a6cda1264206f3e2034fc9982bfd93f261f7"
dependencies = [
 "polkavm-common",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "polkavm-derive-impl-macro"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c16669ddc7433e34c1007d31080b80901e3e8e523cb9d4b441c3910cf9294b"
dependencies = [
 "polkavm-derive-impl",
 "syn 2.0.101",
]

[[package]]
name = "polkavm-linker"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9bfe793b094d9ea5c99b7c43ba46e277b0f8f48f4bbfdbabf8d3ebf701a4bd3"
dependencies = [
 "dirs",
 "gimli 0.31.1",
 "hashbrown 0.14.5",
 "log",
 "object 0.36.7",
 "polkavm-common",
 "regalloc2 0.9.3",
 "rustc-demangle",
]

[[package]]
name = "polkavm-linux-raw"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23eff02c070c70f31878a3d915e88a914ecf3e153741e2fb572dde28cce20fde"

[[package]]
name = "polling"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a604568c3202727d1507653cb121dbd627a58684eb09a820fd746bee38b4442f"
dependencies = [
 "cfg-if",
 "concurrent-queue",
 "hermit-abi 0.4.0",
 "pin-project-lite",
 "rustix 0.38.44",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "poly1305"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8159bd90725d2df49889a078b54f4f79e87f1f8a8444194cdca81d38f5393abf"
dependencies = [
 "cpufeatures",
 "opaque-debug 0.3.1",
 "universal-hash",
]

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "opaque-debug 0.3.1",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "350e9b48cbc6b0e028b0473b114454c6316e57336ee184ceab6e53f72c178b3e"

[[package]]
name = "potential_utf"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5a7c30837279ca13e7c867e9e40053bc68740f988cb07f7ca6df43cc734b585"
dependencies = [
 "zerovec",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85eae3c4ed2f50dcfe72643da4befc30deadb458a9b590d720cde2f2b1e97da9"
dependencies = [
 "zerocopy",
]

[[package]]
name = "predicates"
version = "3.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5d19ee57562043d37e82899fade9a22ebab7be9cef5026b07fda9cdd4293573"
dependencies = [
 "anstyle",
 "predicates-core",
]

[[package]]
name = "predicates-core"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "727e462b119fe9c93fd0eb1429a5f7647394014cf3c04ab2c0350eeb09095ffa"

[[package]]
name = "predicates-tree"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72dd2d6d381dfb73a193c7fca536518d7caee39fc8503f74e7dc0be0531b425c"
dependencies = [
 "predicates-core",
 "termtree",
]

[[package]]
name = "prettyplease"
version = "0.2.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "664ec5419c51e34154eec046ebcba56312d5a2fc3b09a06da188e1ad21afadf6"
dependencies = [
 "proc-macro2",
 "syn 2.0.101",
]

[[package]]
name = "primitive-types"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d15600a7d856470b7d278b3fe0e311fe28c2526348549f8ef2ff7db3299c87f5"
dependencies = [
 "fixed-hash",
 "impl-codec",
 "impl-num-traits",
 "impl-serde",
 "scale-info",
 "uint 0.10.0",
]

[[package]]
name = "prioritized-metered-channel"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a172e6cc603231f2cf004232eabcecccc0da53ba576ab286ef7baa0cfc7927ad"
dependencies = [
 "coarsetime",
 "crossbeam-queue",
 "derive_more 0.99.20",
 "futures",
 "futures-timer",
 "nanorand",
 "thiserror 1.0.69",
 "tracing",
]

[[package]]
name = "proc-macro-crate"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17d47ce914bf4de440332250b0edd23ce48c005f59fab39d3335866b114f11a"
dependencies = [
 "thiserror 1.0.69",
 "toml 0.5.11",
]

[[package]]
name = "proc-macro-crate"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edce586971a4dfaa28950c6f18ed55e0406c1ab88bbce2c6f6293a7aaba73d35"
dependencies = [
 "toml_edit",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "proc-macro-warning"
version = "1.84.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75eea531cfcd120e0851a3f8aed42c4841f78c889eefafd96339c72677ae42c3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "prometheus"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d33c28a30771f7f96db69893f78b857f7450d7e0237e9c8fc6427a81bae7ed1"
dependencies = [
 "cfg-if",
 "fnv",
 "lazy_static",
 "memchr",
 "parking_lot 0.12.3",
 "thiserror 1.0.69",
]

[[package]]
name = "prometheus-client"
version = "0.22.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "504ee9ff529add891127c4827eb481bd69dc0ebc72e9a682e187db4caa60c3ca"
dependencies = [
 "dtoa",
 "itoa",
 "parking_lot 0.12.3",
 "prometheus-client-derive-encode",
]

[[package]]
name = "prometheus-client-derive-encode"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "440f724eba9f6996b75d63681b0a92b06947f1457076d503a4d2e2c8f56442b8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "proptest"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14cae93065090804185d3b75f0bf93b8eeda30c7a9b4a33d3bdb3988d6229e50"
dependencies = [
 "bitflags 2.9.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_xorshift",
 "regex-syntax 0.8.5",
 "unarray",
]

[[package]]
name = "prost"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "deb1435c188b76130da55f17a466d252ff7b1418b2ad3e037d127b94e3411f29"
dependencies = [
 "bytes",
 "prost-derive 0.12.6",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes",
 "prost-derive 0.13.5",
]

[[package]]
name = "prost-build"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be769465445e8c1474e9c5dac2018218498557af32d9ed057325ec9a41ae81bf"
dependencies = [
 "heck 0.5.0",
 "itertools 0.14.0",
 "log",
 "multimap",
 "once_cell",
 "petgraph 0.7.1",
 "prettyplease",
 "prost 0.13.5",
 "prost-types",
 "regex",
 "syn 2.0.101",
 "tempfile",
]

[[package]]
name = "prost-derive"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81bddcdb20abf9501610992b6759a4c888aef7d1a7247ef75e2404275ac24af1"
dependencies = [
 "anyhow",
 "itertools 0.12.1",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prost-types"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52c2c1bf36ddb1a1c396b3601a3cec27c2462e45f07c386894ec3ccf5332bd16"
dependencies = [
 "prost 0.13.5",
]

[[package]]
name = "psm"
version = "0.1.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e944464ec8536cd1beb0bbfd96987eb5e3b72f2ecdafdc5c769a37f1fa2ae1f"
dependencies = [
 "cc",
]

[[package]]
name = "quanta"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bd1fe6824cea6538803de3ff1bc0cf3949024db3d43c9643024bfb33a807c0e"
dependencies = [
 "crossbeam-utils",
 "libc",
 "once_cell",
 "raw-cpuid",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "web-sys",
 "winapi",
]

[[package]]
name = "quick-protobuf"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d6da84cc204722a989e01ba2f6e1e276e190f22263d0cb6ce8526fcdb0d2e1f"
dependencies = [
 "byteorder",
]

[[package]]
name = "quick-protobuf-codec"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15a0580ab32b169745d7a39db2ba969226ca16738931be152a3209b409de2474"
dependencies = [
 "asynchronous-codec 0.7.0",
 "bytes",
 "quick-protobuf",
 "thiserror 1.0.69",
 "unsigned-varint 0.8.0",
]

[[package]]
name = "quinn"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "626214629cda6781b6dc1d316ba307189c85ba657213ce642d9c77670f8202c8"
dependencies = [
 "bytes",
 "cfg_aliases 0.2.1",
 "futures-io",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash 2.1.1",
 "rustls",
 "socket2",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-proto"
version = "0.11.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49df843a9161c85bb8aae55f101bc0bac8bcafd637a620d9122fd7e0b2f7422e"
dependencies = [
 "bytes",
 "getrandom 0.3.3",
 "lru-slab",
 "rand 0.9.1",
 "ring 0.17.14",
 "rustc-hash 2.1.1",
 "rustls",
 "rustls-pki-types",
 "slab",
 "thiserror 2.0.12",
 "tinyvec",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-udp"
version = "0.5.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee4e529991f949c5e25755532370b8af5d114acae52326361d68d47af64aa842"
dependencies = [
 "cfg_aliases 0.2.1",
 "libc",
 "once_cell",
 "socket2",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "quote"
version = "1.0.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1885c039570dc00dcb4ff087a89e185fd56bae234ddc7f056a945bf36467248d"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5"

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.16",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.3",
]

[[package]]
name = "rand_distr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32cb0b9bc82b0a0876c2dd994a7e7a2683d3e7390ca40e6886785ef0c7e3ee31"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "rand_pcg"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59cad018caf63deb318e5a4586d99a24424a364f40f1e5778c29aca23f4fc73e"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "raw-cpuid"
version = "11.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6df7ab838ed27997ba19a4664507e6f82b41fe6e20be42929332156e5e85146"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "rawpointer"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a357793950651c4ed0f3f52338f53b2f809f32d83a07f72909fa13e4c6c1e3"

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "rcgen"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52c4f3084aa3bc7dfbba4eff4fab2a54db4324965d8872ab933565e6fbd83bc6"
dependencies = [
 "pem",
 "ring 0.16.20",
 "time",
 "yasna",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "928fca9cf2aa042393a8325b9ead81d2f0df4cb12e1e24cef072922ccd99c5af"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.16",
 "libredox",
 "thiserror 1.0.69",
]

[[package]]
name = "ref-cast"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a0ae411dbe946a674d89546582cea4ba2bb8defac896622d6496f14c23ba5cf"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1165225c21bff1f3bbce98f5a1f889949bc902d3575308cc7b0de30b4f6d27c7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "regalloc2"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80535183cae11b149d618fbd3c37e38d7cda589d82d7769e196ca9a9042d7621"
dependencies = [
 "fxhash",
 "log",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regalloc2"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad156d539c879b7a24a363a2016d77961786e71f48f2e2fc8302a92abd2429a6"
dependencies = [
 "hashbrown 0.13.2",
 "log",
 "rustc-hash 1.1.0",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "resolv-conf"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc7c8f7f733062b66dc1c63f9db168ac0b97a9210e247fa90fdc9ad08f51b302"

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac 0.12.1",
 "subtle 2.6.1",
]

[[package]]
name = "ring"
version = "0.16.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3053cf52e236a3ed746dfc745aa9cacf1b791d846bdaf412f60a8d7d6e17c8fc"
dependencies = [
 "cc",
 "libc",
 "once_cell",
 "spin 0.5.2",
 "untrusted 0.7.1",
 "web-sys",
 "winapi",
]

[[package]]
name = "ring"
version = "0.17.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4689e6c2294d81e88dc6261c768b63bc4fcdb852be6d1352498b114f61383b7"
dependencies = [
 "cc",
 "cfg-if",
 "getrandom 0.2.16",
 "libc",
 "untrusted 0.9.0",
 "windows-sys 0.52.0",
]

[[package]]
name = "ripemd"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd124222d17ad93a644ed9d011a40f4fb64aa54275c08cc216524a9ea82fb09f"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "rocksdb"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6f170a4041d50a0ce04b0d2e14916d6ca863ea2e422689a5b694395d299ffe"
dependencies = [
 "libc",
 "librocksdb-sys",
]

[[package]]
name = "route-recognizer"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afab94fb28594581f62d981211a9a4d53cc8130bbcbbb89a0440d9b8e81a7746"

[[package]]
name = "rpassword"
version = "7.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66d4c8b64f049c6721ec8ccec37ddfc3d641c4a7fca57e8f2a89de509c73df39"
dependencies = [
 "libc",
 "rtoolbox",
 "windows-sys 0.59.0",
]

[[package]]
name = "rtnetlink"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a552eb82d19f38c3beed3f786bd23aa434ceb9ac43ab44419ca6d67a7e186c0"
dependencies = [
 "futures",
 "log",
 "netlink-packet-core",
 "netlink-packet-route",
 "netlink-packet-utils",
 "netlink-proto",
 "netlink-sys",
 "nix",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "rtoolbox"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7cc970b249fbe527d6e02e0a227762c9108b2f49d81094fe357ffc6d14d7f6f"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc-hex"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e75f6a532d0fd9f7f13144f392b6ad56a32696bfcd9c78f797f16bbb6f072d6"

[[package]]
name = "rustc_version"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcb3a22ef46e85b45de6ee7e79d063319ebb6594faafcf1c225ea92ab6e9b92"
dependencies = [
 "semver 1.0.26",
]

[[package]]
name = "rusticata-macros"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faf0c4a6ece9950b9abdb62b1cfcf2a68b3b67a10ba445b3bb85be2a293d0632"
dependencies = [
 "nom",
]

[[package]]
name = "rustix"
version = "0.36.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "305efbd14fde4139eb501df5f136994bb520b033fa9fbdce287507dc23b8c7ed"
dependencies = [
 "bitflags 1.3.2",
 "errno",
 "io-lifetimes",
 "libc",
 "linux-raw-sys 0.1.4",
 "windows-sys 0.45.0",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.9.0",
 "errno",
 "libc",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c71e83d6afe7ff64890ec6b71d6a69bb8a610ab78ce364b3352876bb4c801266"
dependencies = [
 "bitflags 2.9.0",
 "errno",
 "libc",
 "linux-raw-sys 0.9.4",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls"
version = "0.23.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "730944ca083c1c233a75c09f199e973ca499344a2b7ba9e755c457e86fb4a321"
dependencies = [
 "log",
 "once_cell",
 "ring 0.17.14",
 "rustls-pki-types",
 "rustls-webpki 0.103.3",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcff2dd52b58a8d98a70243663a0d234c4e2b79235637849d15913394a247d3"
dependencies = [
 "openssl-probe",
 "rustls-pki-types",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pki-types"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "229a4a4c221013e7e1f1a043678c5cc39fe5171437c88fb47151a21e6f5b5c79"
dependencies = [
 "web-time",
 "zeroize",
]

[[package]]
name = "rustls-platform-verifier"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19787cda76408ec5404443dc8b31795c87cd8fec49762dc75fa727740d34acc1"
dependencies = [
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "jni",
 "log",
 "once_cell",
 "rustls",
 "rustls-native-certs",
 "rustls-platform-verifier-android",
 "rustls-webpki 0.103.3",
 "security-framework",
 "security-framework-sys",
 "webpki-root-certs 0.26.11",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls-platform-verifier-android"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f87165f0995f63a9fbeea62b64d10b4d9d8e78ec6d7d51fb2125fda7bb36788f"

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring 0.17.14",
 "untrusted 0.9.0",
]

[[package]]
name = "rustls-webpki"
version = "0.103.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4a72fe2bcf7a6ac6fd7d0b9e5cb68aeb7d4c0a0271730218b3e92d43b4eb435"
dependencies = [
 "ring 0.17.14",
 "rustls-pki-types",
 "untrusted 0.9.0",
]

[[package]]
name = "rustversion"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eded382c5f5f786b989652c49544c4877d9f015cc22e145a5ea8ea66c2921cd2"

[[package]]
name = "ruzstd"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5174a470eeb535a721ae9fdd6e291c2411a906b96592182d05217591d5c5cf7b"
dependencies = [
 "byteorder",
 "derive_more 0.99.20",
]

[[package]]
name = "rw-stream-sink"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8c9026ff5d2f23da5e45bbc283f156383001bfb09c4e44256d02c1a685fe9a1"
dependencies = [
 "futures",
 "pin-project",
 "static_assertions",
]

[[package]]
name = "ryu"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28d3b2b1366ec20994f1fd18c3c594f05c5dd4bc44d8bb0c1c632c8d6829481f"

[[package]]
name = "safe_arch"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96b02de82ddbe1b636e6170c21be622223aea188ef2e139be0a5b219ec215323"
dependencies = [
 "bytemuck",
]

[[package]]
name = "salsa20"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97a22f5af31f73a954c10289c93e8a50cc23d971e80ee446f1f6f7137a088213"
dependencies = [
 "cipher 0.4.4",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "sc-allocator"
version = "31.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c10a9966875fcbde028c73697c6d5faad5f5d24e94b3c949fb1d063c727381d"
dependencies = [
 "log",
 "sp-core",
 "sp-wasm-interface",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-authority-discovery"
version = "0.49.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcbf3f7d818fbb607fc6991b603964b2bfe68857cd3f722a87c511d04bb43682"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "ip_network",
 "linked_hash_set",
 "log",
 "parity-scale-codec",
 "prost 0.12.6",
 "prost-build",
 "rand 0.8.5",
 "sc-client-api",
 "sc-network",
 "sc-network-types",
 "sp-api",
 "sp-authority-discovery",
 "sp-blockchain",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-basic-authorship"
version = "0.49.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9132e1990352be9840c18186e4ce3e810dfc146728ced1ac6b2da4eb794a547"
dependencies = [
 "futures",
 "log",
 "parity-scale-codec",
 "sc-block-builder",
 "sc-proposer-metrics",
 "sc-telemetry",
 "sc-transaction-pool-api",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-block-builder"
version = "0.44.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6622da4fe938fed2f4e0f127c92cee835dedc325fb4c2358c03912232beee24"
dependencies = [
 "parity-scale-codec",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-trie",
]

[[package]]
name = "sc-chain-spec"
version = "42.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1ca4ca82a725cc03078839d823ed0f999507ffd0b9a3b24a5f21cf10f24e2e0"
dependencies = [
 "array-bytes",
 "docify",
 "memmap2 0.9.5",
 "parity-scale-codec",
 "sc-chain-spec-derive",
 "sc-client-api",
 "sc-executor",
 "sc-network",
 "sc-telemetry",
 "serde",
 "serde_json",
 "sp-blockchain",
 "sp-core",
 "sp-crypto-hashing",
 "sp-genesis-builder",
 "sp-io",
 "sp-runtime",
 "sp-state-machine",
 "sp-tracing",
]

[[package]]
name = "sc-chain-spec-derive"
version = "12.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b18cef11d2c69703e0d7c3528202ef4ed1cd2b47a6f063e9e17cad8255b1fa94"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sc-cli"
version = "0.51.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea0f6424aacf055719cee01d59a1153aa8aed9739cff3beec0aea5675b13afe"
dependencies = [
 "array-bytes",
 "chrono",
 "clap",
 "fdlimit",
 "futures",
 "itertools 0.11.0",
 "libp2p-identity",
 "log",
 "names",
 "parity-bip39",
 "parity-scale-codec",
 "rand 0.8.5",
 "regex",
 "rpassword",
 "sc-client-api",
 "sc-client-db",
 "sc-keystore",
 "sc-mixnet",
 "sc-network",
 "sc-service",
 "sc-telemetry",
 "sc-tracing",
 "sc-transaction-pool",
 "sc-utils",
 "serde",
 "serde_json",
 "sp-blockchain",
 "sp-core",
 "sp-keyring",
 "sp-keystore",
 "sp-panic-handler",
 "sp-runtime",
 "sp-version",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "sc-client-api"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace1a9f5b53e738a353079a5e5a41e55fa62887cc1d7491b97feca6847b4f88d"
dependencies = [
 "fnv",
 "futures",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-executor",
 "sc-transaction-pool-api",
 "sc-utils",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-database",
 "sp-externalities",
 "sp-runtime",
 "sp-state-machine",
 "sp-storage",
 "sp-trie",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-client-db"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dfc8b2f7156ced83fc9e52a610b580a54d2499e7674f8f629ea3a11e4c2d0e9"
dependencies = [
 "hash-db",
 "kvdb",
 "kvdb-memorydb",
 "kvdb-rocksdb",
 "linked-hash-map",
 "log",
 "parity-db",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-client-api",
 "sc-state-db",
 "schnellru",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-core",
 "sp-database",
 "sp-runtime",
 "sp-state-machine",
 "sp-trie",
]

[[package]]
name = "sc-consensus"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15adbad0ca8f3312ff19ec97ef75bce5809518ff4725121e7885d42bb8de5fea"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "mockall",
 "parking_lot 0.12.3",
 "sc-client-api",
 "sc-network-types",
 "sc-utils",
 "serde",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-runtime",
 "sp-state-machine",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-consensus-aura"
version = "0.49.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5863f442a228f9cee8c5e75a9abda97f3b9a2dd8221b0e0a9201319fc4b9313d"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "parity-scale-codec",
 "sc-block-builder",
 "sc-client-api",
 "sc-consensus",
 "sc-consensus-slots",
 "sc-telemetry",
 "sp-api",
 "sp-application-crypto",
 "sp-block-builder",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-aura",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-keystore",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-consensus-grandpa"
version = "0.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c338eea1825f212308cc89c5d7db38810cac042da942347fa889e27fd7d8d8d"
dependencies = [
 "ahash",
 "array-bytes",
 "async-trait",
 "dyn-clone",
 "finality-grandpa",
 "fork-tree",
 "futures",
 "futures-timer",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-client-api",
 "sc-consensus",
 "sc-network",
 "sc-network-common",
 "sc-network-gossip",
 "sc-network-sync",
 "sc-network-types",
 "sc-telemetry",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde_json",
 "sp-api",
 "sp-application-crypto",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-crypto-hashing",
 "sp-keystore",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-consensus-slots"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "701fd4b43e453030db70ace3d706309fbbd84096929f96a4efa96f5f22121148"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "log",
 "parity-scale-codec",
 "sc-client-api",
 "sc-consensus",
 "sc-telemetry",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-state-machine",
]

[[package]]
name = "sc-executor"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b55c745bf88acb34bd606346c7de6cc06f334f627c1ff40380252a6e52ad9354"
dependencies = [
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-executor-common",
 "sc-executor-polkavm",
 "sc-executor-wasmtime",
 "schnellru",
 "sp-api",
 "sp-core",
 "sp-externalities",
 "sp-io",
 "sp-panic-handler",
 "sp-runtime-interface",
 "sp-trie",
 "sp-version",
 "sp-wasm-interface",
 "tracing",
]

[[package]]
name = "sc-executor-common"
version = "0.38.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2f84b9aa7664a9b401afbf423bcd3c1845f5adedf4f6030586808238a222df"
dependencies = [
 "polkavm",
 "sc-allocator",
 "sp-maybe-compressed-blob",
 "sp-wasm-interface",
 "thiserror 1.0.69",
 "wasm-instrument",
]

[[package]]
name = "sc-executor-polkavm"
version = "0.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7eb4929b3457077f9b30ad397a724116f43f252a889ec334ec369f6cdad8f76c"
dependencies = [
 "log",
 "polkavm",
 "sc-executor-common",
 "sp-wasm-interface",
]

[[package]]
name = "sc-executor-wasmtime"
version = "0.38.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b5ad79b030a1f91ef0f667e58ac35e1c9fa33a6b8a0ec1ae7fe4890322535ac"
dependencies = [
 "anyhow",
 "log",
 "parking_lot 0.12.3",
 "rustix 0.36.17",
 "sc-allocator",
 "sc-executor-common",
 "sp-runtime-interface",
 "sp-wasm-interface",
 "wasmtime",
]

[[package]]
name = "sc-informant"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e979f8ccece43aa2d693bf9d4ca1830ac7155293951cdb6da40f1b28687baea"
dependencies = [
 "console",
 "futures",
 "futures-timer",
 "log",
 "sc-client-api",
 "sc-network",
 "sc-network-sync",
 "sp-blockchain",
 "sp-runtime",
]

[[package]]
name = "sc-keystore"
version = "35.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6277839ec26d67fbef7d6c87e8f34c814656c8d51433d345d862164adb3f5c2e"
dependencies = [
 "array-bytes",
 "parking_lot 0.12.3",
 "serde_json",
 "sp-application-crypto",
 "sp-core",
 "sp-keystore",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-mixnet"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a4fd83a76b5a6a715a2567b762637cbc26c2f1199c8698e0603242069a6ef60"
dependencies = [
 "array-bytes",
 "arrayvec 0.7.6",
 "blake2 0.10.6",
 "bytes",
 "futures",
 "futures-timer",
 "log",
 "mixnet",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-client-api",
 "sc-network",
 "sc-network-types",
 "sc-transaction-pool-api",
 "sp-api",
 "sp-consensus",
 "sp-core",
 "sp-keystore",
 "sp-mixnet",
 "sp-runtime",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-network"
version = "0.49.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df65eb7a3c4c141de3f14b12a9832c75c7ada1fd580b0bc440263cb8ca2c5b77"
dependencies = [
 "array-bytes",
 "async-channel 1.9.0",
 "async-trait",
 "asynchronous-codec 0.6.2",
 "bytes",
 "cid 0.9.0",
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "ip_network",
 "libp2p",
 "linked_hash_set",
 "litep2p",
 "log",
 "mockall",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "partial_sort",
 "pin-project",
 "prost 0.12.6",
 "prost-build",
 "rand 0.8.5",
 "sc-client-api",
 "sc-network-common",
 "sc-network-types",
 "sc-utils",
 "schnellru",
 "serde",
 "serde_json",
 "smallvec",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "unsigned-varint 0.7.2",
 "void",
 "wasm-timer",
 "zeroize",
]

[[package]]
name = "sc-network-common"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2a5fc004d848bf6c1dc3cc433a0d5166dc7735ec7eb17023eff046c948c174d"
dependencies = [
 "bitflags 1.3.2",
 "parity-scale-codec",
 "sp-runtime",
]

[[package]]
name = "sc-network-gossip"
version = "0.49.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1827988c88bc075995ec11bdd0ca9f928909cbf5fef5abb33a4cdffa1f99cdb"
dependencies = [
 "ahash",
 "futures",
 "futures-timer",
 "log",
 "sc-network",
 "sc-network-common",
 "sc-network-sync",
 "sc-network-types",
 "schnellru",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "tracing",
]

[[package]]
name = "sc-network-light"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc00bf32b686d3f25e7697fb40d20bc0654ac2ddc7c03fc641246f40d76af2da"
dependencies = [
 "array-bytes",
 "async-channel 1.9.0",
 "futures",
 "log",
 "parity-scale-codec",
 "prost 0.12.6",
 "prost-build",
 "sc-client-api",
 "sc-network",
 "sc-network-types",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-network-sync"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d22f0e1c117901ac5ba27df45a34928ff485741b8300809e2fdd812208020eb"
dependencies = [
 "array-bytes",
 "async-channel 1.9.0",
 "async-trait",
 "fork-tree",
 "futures",
 "log",
 "mockall",
 "parity-scale-codec",
 "prost 0.12.6",
 "prost-build",
 "sc-client-api",
 "sc-consensus",
 "sc-network",
 "sc-network-common",
 "sc-network-types",
 "sc-utils",
 "schnellru",
 "smallvec",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "sc-network-transactions"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0443ceff666e09504981eb10da28d0e959276fc713792a23586e01132100a49a"
dependencies = [
 "array-bytes",
 "futures",
 "log",
 "parity-scale-codec",
 "sc-network",
 "sc-network-common",
 "sc-network-sync",
 "sc-network-types",
 "sc-utils",
 "sp-consensus",
 "sp-runtime",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-network-types"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ff910b7a20f14b1a77b2616de21d509cf51ce1a006e30b2d1f293a8fae72555"
dependencies = [
 "bs58",
 "bytes",
 "ed25519-dalek",
 "libp2p-identity",
 "libp2p-kad",
 "litep2p",
 "log",
 "multiaddr 0.18.2",
 "multihash 0.19.3",
 "rand 0.8.5",
 "thiserror 1.0.69",
 "zeroize",
]

[[package]]
name = "sc-offchain"
version = "44.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "721798adf89fdf7068912c91cae8b7174bb561201a54ba7b788ae2ef36da154d"
dependencies = [
 "bytes",
 "fnv",
 "futures",
 "futures-timer",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-rustls",
 "hyper-util",
 "num_cpus",
 "once_cell",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "rustls",
 "sc-client-api",
 "sc-network",
 "sc-network-types",
 "sc-transaction-pool-api",
 "sc-utils",
 "sp-api",
 "sp-core",
 "sp-externalities",
 "sp-keystore",
 "sp-offchain",
 "sp-runtime",
 "threadpool",
 "tracing",
]

[[package]]
name = "sc-proposer-metrics"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "872046dabf12aef8cdc6a67a9c5bcb4fc34fb7f2d8a664ed2028aaf2717895f1"
dependencies = [
 "log",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-rpc"
version = "44.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf313ac99a06ececd9576909c5fc688a6e22c60997fa1b8a58035f4ff1e861bf"
dependencies = [
 "futures",
 "jsonrpsee",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-client-api",
 "sc-mixnet",
 "sc-rpc-api",
 "sc-tracing",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde_json",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-keystore",
 "sp-offchain",
 "sp-rpc",
 "sp-runtime",
 "sp-session",
 "sp-statement-store",
 "sp-version",
 "tokio",
]

[[package]]
name = "sc-rpc-api"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed810a156f70cf5f7ab8fb5d9cf818999a72821c570aba4f4699aa4eea59e01"
dependencies = [
 "jsonrpsee",
 "parity-scale-codec",
 "sc-chain-spec",
 "sc-mixnet",
 "sc-transaction-pool-api",
 "scale-info",
 "serde",
 "serde_json",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-version",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-rpc-server"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12c7a0a366367b1a3480af1327cd7d841806edc7f46da485e2c36064ad98a7c5"
dependencies = [
 "dyn-clone",
 "forwarded-header-value",
 "futures",
 "governor",
 "http 1.3.1",
 "http-body-util",
 "hyper 1.6.0",
 "ip_network",
 "jsonrpsee",
 "log",
 "sc-rpc-api",
 "serde",
 "serde_json",
 "substrate-prometheus-endpoint",
 "tokio",
 "tower",
 "tower-http",
]

[[package]]
name = "sc-rpc-spec-v2"
version = "0.49.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb04221e5ca2f9a92fc12336c7bb8a04c55173cfe505e207365b1ea3e1352d6b"
dependencies = [
 "array-bytes",
 "futures",
 "futures-util",
 "hex",
 "itertools 0.11.0",
 "jsonrpsee",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "sc-chain-spec",
 "sc-client-api",
 "sc-rpc",
 "sc-transaction-pool-api",
 "schnellru",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-version",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "sc-runtime-utilities"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb39eaa0993635be94a5d5171f75ed81b7149cfcf435725581ee968d9d9b563f"
dependencies = [
 "parity-scale-codec",
 "sc-executor",
 "sc-executor-common",
 "sp-core",
 "sp-crypto-hashing",
 "sp-state-machine",
 "sp-wasm-interface",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-service"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0204f65df1d1cf22c6fb63f5268132468261520aa66943bd37d59a61b8241322"
dependencies = [
 "async-trait",
 "directories",
 "exit-future",
 "futures",
 "futures-timer",
 "jsonrpsee",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "sc-chain-spec",
 "sc-client-api",
 "sc-client-db",
 "sc-consensus",
 "sc-executor",
 "sc-informant",
 "sc-keystore",
 "sc-network",
 "sc-network-common",
 "sc-network-light",
 "sc-network-sync",
 "sc-network-transactions",
 "sc-network-types",
 "sc-rpc",
 "sc-rpc-server",
 "sc-rpc-spec-v2",
 "sc-sysinfo",
 "sc-telemetry",
 "sc-tracing",
 "sc-transaction-pool",
 "sc-transaction-pool-api",
 "sc-utils",
 "schnellru",
 "serde",
 "serde_json",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-externalities",
 "sp-keystore",
 "sp-runtime",
 "sp-session",
 "sp-state-machine",
 "sp-storage",
 "sp-transaction-pool",
 "sp-transaction-storage-proof",
 "sp-trie",
 "sp-version",
 "static_init",
 "substrate-prometheus-endpoint",
 "tempfile",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "sc-state-db"
version = "0.38.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b4a6610694637ad5e54ddd6af421178e23353443893213c0c2eb31344b65cd5"
dependencies = [
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sp-core",
]

[[package]]
name = "sc-sysinfo"
version = "42.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "beadd799aae2a1ac6eac8edcbcfba533ae4aadbf2c7e8449f530fd98b5be7cd9"
dependencies = [
 "derive_more 0.99.20",
 "futures",
 "libc",
 "log",
 "rand 0.8.5",
 "rand_pcg",
 "regex",
 "sc-telemetry",
 "serde",
 "serde_json",
 "sp-core",
 "sp-crypto-hashing",
 "sp-io",
]

[[package]]
name = "sc-telemetry"
version = "28.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d751fd77c6a8d1a5bca8cb5df9d9c57f77b4b15e84eab07925b0f76ddee3e74"
dependencies = [
 "chrono",
 "futures",
 "libp2p",
 "log",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "sc-utils",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "wasm-timer",
]

[[package]]
name = "sc-tracing"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97765091d1e29f00bc0ab13149b1922c89dd60b66069e1016a9eb4b289171e3"
dependencies = [
 "chrono",
 "console",
 "is-terminal",
 "libc",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rustc-hash 1.1.0",
 "sc-client-api",
 "sc-tracing-proc-macro",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-tracing",
 "thiserror 1.0.69",
 "tracing",
 "tracing-log",
 "tracing-subscriber",
]

[[package]]
name = "sc-tracing-proc-macro"
version = "11.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb090b3a15c077b029619476b682ba8a31f391ee3f0b2c5f3f24366f53f6c538"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sc-transaction-pool"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db819d511819f146c5b4d6c2d75aaf1aaad6045b4644ce8528bfa300a621790a"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "indexmap 2.9.0",
 "itertools 0.11.0",
 "linked-hash-map",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sc-client-api",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-crypto-hashing",
 "sp-runtime",
 "sp-tracing",
 "sp-transaction-pool",
 "substrate-prometheus-endpoint",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "sc-transaction-pool-api"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55feca303d4ba839f02261c9a73d40f6b0ac7523882b4008472922b934678729"
dependencies = [
 "async-trait",
 "futures",
 "indexmap 2.9.0",
 "log",
 "parity-scale-codec",
 "serde",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "thiserror 1.0.69",
]

[[package]]
name = "sc-utils"
version = "18.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d8879d46892f1378ff633692740d0a5cb13777ee6dafe84d7e9b954b1e6753"
dependencies = [
 "async-channel 1.9.0",
 "futures",
 "futures-timer",
 "log",
 "parking_lot 0.12.3",
 "prometheus",
 "sp-arithmetic",
]

[[package]]
name = "scale-bits"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e57b1e7f6b65ed1f04e79a85a57d755ad56d76fdf1e9bddcc9ae14f71fcdcf54"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "scale-type-resolver",
 "serde",
]

[[package]]
name = "scale-decode"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e98f3262c250d90e700bb802eb704e1f841e03331c2eb815e46516c4edbf5b27"
dependencies = [
 "derive_more 0.99.20",
 "parity-scale-codec",
 "scale-bits",
 "scale-type-resolver",
 "smallvec",
]

[[package]]
name = "scale-decode"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ae9cc099ae85ff28820210732b00f019546f36f33225f509fe25d5816864a0"
dependencies = [
 "derive_more 1.0.0",
 "parity-scale-codec",
 "primitive-types",
 "scale-bits",
 "scale-decode-derive",
 "scale-type-resolver",
 "smallvec",
]

[[package]]
name = "scale-decode-derive"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ed9401effa946b493f9f84dc03714cca98119b230497df6f3df6b84a2b03648"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "scale-encode"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9271284d05d0749c40771c46180ce89905fd95aa72a2a2fddb4b7c0aa424db"
dependencies = [
 "derive_more 1.0.0",
 "parity-scale-codec",
 "primitive-types",
 "scale-bits",
 "scale-encode-derive",
 "scale-type-resolver",
 "smallvec",
]

[[package]]
name = "scale-encode-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "102fbc6236de6c53906c0b262f12c7aa69c2bdc604862c12728f5f4d370bc137"
dependencies = [
 "darling",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "scale-info"
version = "2.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "346a3b32eba2640d17a9cb5927056b08f3de90f65b72fe09402c2ad07d684d0b"
dependencies = [
 "bitvec",
 "cfg-if",
 "derive_more 1.0.0",
 "parity-scale-codec",
 "scale-info-derive",
 "serde",
]

[[package]]
name = "scale-info-derive"
version = "2.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6630024bf739e2179b91fb424b28898baf819414262c5d376677dbff1fe7ebf"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "scale-type-resolver"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0cded6518aa0bd6c1be2b88ac81bf7044992f0f154bfbabd5ad34f43512abcb"
dependencies = [
 "scale-info",
 "smallvec",
]

[[package]]
name = "scale-typegen"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc4c70c7fea2eef1740f0081d3fe385d8bee1eef11e9272d3bec7dc8e5438e0"
dependencies = [
 "proc-macro2",
 "quote",
 "scale-info",
 "syn 2.0.101",
 "thiserror 1.0.69",
]

[[package]]
name = "scale-value"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5e0ef2a0ee1e02a69ada37feb87ea1616ce9808aca072befe2d3131bf28576e"
dependencies = [
 "base58",
 "blake2 0.10.6",
 "derive_more 1.0.0",
 "either",
 "parity-scale-codec",
 "scale-bits",
 "scale-decode 0.14.0",
 "scale-encode",
 "scale-info",
 "scale-type-resolver",
 "serde",
 "yap",
]

[[package]]
name = "schannel"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f29ebaa345f945cec9fbbc532eb307f0fdad8161f281b6369539c8d84876b3d"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "schnellru"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "356285bbf17bea63d9e52e96bd18f039672ac92b55b8cb997d6162a2a37d1649"
dependencies = [
 "ahash",
 "cfg-if",
 "hashbrown 0.13.2",
]

[[package]]
name = "schnorrkel"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de18f6d8ba0aad7045f5feae07ec29899c1112584a38509a84ad7b04451eaa0"
dependencies = [
 "aead",
 "arrayref",
 "arrayvec 0.7.6",
 "curve25519-dalek",
 "getrandom_or_panic",
 "merlin",
 "rand_core 0.6.4",
 "serde_bytes",
 "sha2 0.10.9",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scratch"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f6280af86e5f559536da57a45ebc84948833b3bee313a7dd25232e09c878a52"

[[package]]
name = "scrypt"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0516a385866c09368f0b5bcd1caff3366aace790fcd46e2bb032697bb172fd1f"
dependencies = [
 "password-hash",
 "pbkdf2",
 "salsa20",
 "sha2 0.10.9",
]

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct",
 "der",
 "generic-array 0.14.7",
 "pkcs8",
 "serdect",
 "subtle 2.6.1",
 "zeroize",
]

[[package]]
name = "secp256k1"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25996b82292a7a57ed3508f052cfff8640d38d32018784acd714758b43da9c8f"
dependencies = [
 "secp256k1-sys 0.8.1",
]

[[package]]
name = "secp256k1"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d24b59d129cdadea20aea4fb2352fa053712e5d713eee47d700cd4b2bc002f10"
dependencies = [
 "secp256k1-sys 0.9.2",
]

[[package]]
name = "secp256k1"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b50c5943d326858130af85e049f2661ba3c78b26589b8ab98e65e80ae44a1252"
dependencies = [
 "bitcoin_hashes 0.14.0",
 "rand 0.8.5",
 "secp256k1-sys 0.10.1",
]

[[package]]
name = "secp256k1-sys"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70a129b9e9efbfb223753b9163c4ab3b13cff7fd9c7f010fbac25ab4099fa07e"
dependencies = [
 "cc",
]

[[package]]
name = "secp256k1-sys"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d1746aae42c19d583c3c1a8c646bfad910498e2051c551a7f2e3c0c9fbb7eb"
dependencies = [
 "cc",
]

[[package]]
name = "secp256k1-sys"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4387882333d3aa8cb20530a17c69a3752e97837832f34f6dccc760e715001d9"
dependencies = [
 "cc",
]

[[package]]
name = "secrecy"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bd1c54ea06cfd2f6b63219704de0b9b4f72dcc2b8fdef820be6cd799780e91e"
dependencies = [
 "zeroize",
]

[[package]]
name = "secrecy"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e891af845473308773346dc847b2c23ee78fe442e0472ac50e22a18a93d3ae5a"
dependencies = [
 "zeroize",
]

[[package]]
name = "security-framework"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271720403f46ca04f7ba6f55d438f8bd878d6b8ca0a1046e8228c4145bcbb316"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a3186ec9e65071a2095434b1f5bb24838d4e8e130f584c790f6033c79943537"
dependencies = [
 "semver-parser",
]

[[package]]
name = "semver"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e6fa9c48d24d85fb3de5ad847117517440f6beceb7798af16b4a87d616b8d0"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_bytes"
version = "0.11.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8437fd221bde2d4ca316d61b90e337e9e702b3820b87d63caa9ba6c02bd06d96"
dependencies = [
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_json"
version = "1.0.140"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20068b6e96dc6c9bd23e01df8827e6c7e1f2fddd43c21810382803c136b99373"
dependencies = [
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_spanned"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87607cb1398ed59d48732e575a4c28a7a8ebf2454b964fe3f224f2afc07909e1"
dependencies = [
 "serde",
]

[[package]]
name = "serdect"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a84f14a19e9a014bb9f4512488d9829a68e04ecabffb0f9904cd1ace94598177"
dependencies = [
 "base16ct",
 "serde",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug 0.3.1",
]

[[package]]
name = "sha2"
version = "0.10.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7507d819769d01a365ab707794a4084392c824f54a7a6a7862f8c3d0892b283"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook-registry"
version = "1.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9203b8055f63a2a00e2f593bb0510367fe707d7ff1e5c872de2f537b339e5410"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "simba"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3a386a501cd104797982c15ae17aafe8b9261315b5d07e3ec803f2ea26be0fa"
dependencies = [
 "approx",
 "num-complex",
 "num-traits",
 "paste",
 "wide",
]

[[package]]
name = "simple-dns"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dee851d0e5e7af3721faea1843e8015e820a234f81fda3dea9247e15bac9a86a"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "simple-mermaid"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "620a1d43d70e142b1d46a929af51d44f383db9c7a2ec122de2cd992ccfcf3c18"

[[package]]
name = "siphasher"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38b58827f4464d87d377d175e90bf58eb00fd8716ff0a62f80356b5e61555d0d"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "smallvec"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8917285742e9f3e1683f0a9c4e6b57960b7314d0b08d30d1ecd426713ee2eee9"

[[package]]
name = "smol"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a33bd3e260892199c3ccfc487c88b2da2265080acb316cd920da72fdfd7c599f"
dependencies = [
 "async-channel 2.3.1",
 "async-executor",
 "async-fs",
 "async-io",
 "async-lock",
 "async-net",
 "async-process",
 "blocking",
 "futures-lite",
]

[[package]]
name = "smoldot"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "966e72d77a3b2171bb7461d0cb91f43670c63558c62d7cf42809cae6c8b6b818"
dependencies = [
 "arrayvec 0.7.6",
 "async-lock",
 "atomic-take",
 "base64 0.22.1",
 "bip39",
 "blake2-rfc",
 "bs58",
 "chacha20",
 "crossbeam-queue",
 "derive_more 0.99.20",
 "ed25519-zebra",
 "either",
 "event-listener 5.4.0",
 "fnv",
 "futures-lite",
 "futures-util",
 "hashbrown 0.14.5",
 "hex",
 "hmac 0.12.1",
 "itertools 0.13.0",
 "libm",
 "libsecp256k1",
 "merlin",
 "nom",
 "num-bigint",
 "num-rational",
 "num-traits",
 "pbkdf2",
 "pin-project",
 "poly1305",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "ruzstd",
 "schnorrkel",
 "serde",
 "serde_json",
 "sha2 0.10.9",
 "sha3",
 "siphasher 1.0.1",
 "slab",
 "smallvec",
 "soketto",
 "twox-hash",
 "wasmi",
 "x25519-dalek",
 "zeroize",
]

[[package]]
name = "smoldot-light"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a33b06891f687909632ce6a4e3fd7677b24df930365af3d0bcb078310129f3f"
dependencies = [
 "async-channel 2.3.1",
 "async-lock",
 "base64 0.22.1",
 "blake2-rfc",
 "bs58",
 "derive_more 0.99.20",
 "either",
 "event-listener 5.4.0",
 "fnv",
 "futures-channel",
 "futures-lite",
 "futures-util",
 "hashbrown 0.14.5",
 "hex",
 "itertools 0.13.0",
 "log",
 "lru",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "serde",
 "serde_json",
 "siphasher 1.0.1",
 "slab",
 "smol",
 "smoldot",
 "zeroize",
]

[[package]]
name = "snap"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b6b67fb9a61334225b5b790716f609cd58395f895b3fe8b328786812a40bc3b"

[[package]]
name = "snow"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "850948bee068e713b8ab860fe1adc4d109676ab4c3b621fd8147f06b261f2f85"
dependencies = [
 "aes-gcm",
 "blake2 0.10.6",
 "chacha20poly1305",
 "curve25519-dalek",
 "rand_core 0.6.4",
 "ring 0.17.14",
 "rustc_version",
 "sha2 0.10.9",
 "subtle 2.6.1",
]

[[package]]
name = "socket2"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f5fd57c80058a56cf5c777ab8a126398ece8e442983605d280a44ce79d0edef"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "soketto"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e859df029d160cb88608f5d7df7fb4753fd20fdfb4de5644f3d8b8440841721"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "futures",
 "http 1.3.1",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
]

[[package]]
name = "solochain-template-node"
version = "0.1.0"
dependencies = [
 "clap",
 "frame-benchmarking-cli",
 "frame-metadata-hash-extension",
 "frame-system",
 "futures",
 "jsonrpsee",
 "pallet-transaction-payment",
 "pallet-transaction-payment-rpc",
 "sc-basic-authorship",
 "sc-cli",
 "sc-client-api",
 "sc-consensus",
 "sc-consensus-aura",
 "sc-consensus-grandpa",
 "sc-executor",
 "sc-network",
 "sc-offchain",
 "sc-service",
 "sc-telemetry",
 "sc-transaction-pool",
 "sc-transaction-pool-api",
 "solochain-template-runtime",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-consensus-aura",
 "sp-core",
 "sp-genesis-builder",
 "sp-inherents",
 "sp-io",
 "sp-keyring",
 "sp-runtime",
 "sp-timestamp",
 "substrate-build-script-utils",
 "substrate-frame-rpc-system",
]

[[package]]
name = "solochain-template-runtime"
version = "0.1.0"
dependencies = [
 "frame-benchmarking",
 "frame-executive",
 "frame-metadata-hash-extension",
 "frame-support",
 "frame-system",
 "frame-system-benchmarking",
 "frame-system-rpc-runtime-api",
 "frame-try-runtime",
 "pallet-aura",
 "pallet-balances",
 "pallet-grandpa",
 "pallet-sudo",
 "pallet-template",
 "pallet-timestamp",
 "pallet-transaction-payment",
 "pallet-transaction-payment-rpc-runtime-api",
 "parity-scale-codec",
 "scale-info",
 "serde_json",
 "sp-api",
 "sp-block-builder",
 "sp-consensus-aura",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-genesis-builder",
 "sp-inherents",
 "sp-keyring",
 "sp-offchain",
 "sp-runtime",
 "sp-session",
 "sp-storage",
 "sp-transaction-pool",
 "sp-version",
 "substrate-wasm-builder",
]

[[package]]
name = "sp-api"
version = "36.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541da427f47dfb97f3dd0556fa3272bdc5dfa0d4c1ad53a22670a9bae4db63d7"
dependencies = [
 "docify",
 "hash-db",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api-proc-macro",
 "sp-core",
 "sp-externalities",
 "sp-metadata-ir",
 "sp-runtime",
 "sp-runtime-interface",
 "sp-state-machine",
 "sp-trie",
 "sp-version",
 "thiserror 1.0.69",
]

[[package]]
name = "sp-api-proc-macro"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36334085c348bb507debd40e604f71194b1fc669eb6fec81aebef08eb3466f6c"
dependencies = [
 "Inflector",
 "blake2 0.10.6",
 "expander",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sp-application-crypto"
version = "40.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba375ab65a76f7413d1bfe48122fd347ce7bd2047e36ecbbd78f12f5adaed121"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
]

[[package]]
name = "sp-arithmetic"
version = "26.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9971b30935cea3858664965039dabd80f67aca74cc6cc6dd42ff1ab14547bc53"
dependencies = [
 "docify",
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "static_assertions",
]

[[package]]
name = "sp-authority-discovery"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55fc2f6c59c333eef805edcec5e603dd8e3a94e20fddb6b19cb914c9f3be7ad5"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api",
 "sp-application-crypto",
 "sp-runtime",
]

[[package]]
name = "sp-block-builder"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a165b95a2f03d9c09c3e51ac3f23d27b091543a41cd3b3df1348aa5917d01eca"
dependencies = [
 "sp-api",
 "sp-inherents",
 "sp-runtime",
]

[[package]]
name = "sp-blockchain"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0afbe184cfe66895497cdfac1ab2927d85294b9c3bcc2c734798994d08b95db6"
dependencies = [
 "futures",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "schnellru",
 "sp-api",
 "sp-consensus",
 "sp-core",
 "sp-database",
 "sp-runtime",
 "sp-state-machine",
 "thiserror 1.0.69",
 "tracing",
]

[[package]]
name = "sp-consensus"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f5fed2e52d0cbf8ddc39a5bb7211f19a26f15f70a6c8d964ee05fc73b64e6c3"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "sp-inherents",
 "sp-runtime",
 "sp-state-machine",
 "thiserror 1.0.69",
]

[[package]]
name = "sp-consensus-aura"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4f3b3414e7620ad72d0000b520e0570dca38dc63e160c95164ff3f789020cc1"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "sp-api",
 "sp-application-crypto",
 "sp-consensus-slots",
 "sp-inherents",
 "sp-runtime",
 "sp-timestamp",
]

[[package]]
name = "sp-consensus-babe"
version = "0.42.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b54310103ae4f0e3228e217e2a9ccaca0d7c3502d3aa276623febf4c722ca397"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-timestamp",
]

[[package]]
name = "sp-consensus-grandpa"
version = "23.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1e969d551ce631fbaf190a4457c295ef70c50bae657602f2377e433f9454868"
dependencies = [
 "finality-grandpa",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
]

[[package]]
name = "sp-consensus-slots"
version = "0.42.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc83d9e7b1d58e1d020c20d7208b00d21fa73dcf92721114eae432b9f01e62d5"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-timestamp",
]

[[package]]
name = "sp-core"
version = "36.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cdbb58c21e6b27f2aadf3ff0c8b20a8ead13b9dfe63f46717fd59334517f3b4"
dependencies = [
 "ark-vrf",
 "array-bytes",
 "bitflags 1.3.2",
 "blake2 0.10.6",
 "bounded-collections",
 "bs58",
 "dyn-clonable",
 "ed25519-zebra",
 "futures",
 "hash-db",
 "hash256-std-hasher",
 "impl-serde",
 "itertools 0.11.0",
 "k256",
 "libsecp256k1",
 "log",
 "merlin",
 "parity-bip39",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "paste",
 "primitive-types",
 "rand 0.8.5",
 "scale-info",
 "schnorrkel",
 "secp256k1 0.28.2",
 "secrecy 0.8.0",
 "serde",
 "sp-crypto-hashing",
 "sp-debug-derive",
 "sp-externalities",
 "sp-runtime-interface",
 "sp-std",
 "sp-storage",
 "ss58-registry",
 "substrate-bip39",
 "thiserror 1.0.69",
 "tracing",
 "w3f-bls",
 "zeroize",
]

[[package]]
name = "sp-crypto-hashing"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc9927a7f81334ed5b8a98a4a978c81324d12bd9713ec76b5c68fd410174c5eb"
dependencies = [
 "blake2b_simd",
 "byteorder",
 "digest 0.10.7",
 "sha2 0.10.9",
 "sha3",
 "twox-hash",
]

[[package]]
name = "sp-crypto-hashing-proc-macro"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b85d0f1f1e44bd8617eb2a48203ee854981229e3e79e6f468c7175d5fd37489b"
dependencies = [
 "quote",
 "sp-crypto-hashing",
 "syn 2.0.101",
]

[[package]]
name = "sp-database"
version = "10.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "722cbecdbf5b94578137dbd07feb51e95f7de221be0c1ff4dcfe0bb4cd986929"
dependencies = [
 "kvdb",
 "parking_lot 0.12.3",
]

[[package]]
name = "sp-debug-derive"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d09fa0a5f7299fb81ee25ae3853d26200f7a348148aed6de76be905c007dbe"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sp-externalities"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30cbf059dce180a8bf8b6c8b08b6290fa3d1c7f069a60f1df038ab5dd5fc0ba6"
dependencies = [
 "environmental",
 "parity-scale-codec",
 "sp-storage",
]

[[package]]
name = "sp-genesis-builder"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efb0d996dfce9afb8879bdfbba9cb9a7d06f29fda38168b91e90419b3b92c42e"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde_json",
 "sp-api",
 "sp-runtime",
]

[[package]]
name = "sp-inherents"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb09ff07946f3e1ecdd4bfb40b2cceba60188215ceb941b5b07230294d7aee1"
dependencies = [
 "async-trait",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime",
 "thiserror 1.0.69",
]

[[package]]
name = "sp-io"
version = "40.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e41d010bcc515d119901ff7ac83150c335d543c7f6c03be5c8fe08430b8a03b"
dependencies = [
 "bytes",
 "docify",
 "ed25519-dalek",
 "libsecp256k1",
 "log",
 "parity-scale-codec",
 "polkavm-derive",
 "rustversion",
 "secp256k1 0.28.2",
 "sp-core",
 "sp-crypto-hashing",
 "sp-externalities",
 "sp-keystore",
 "sp-runtime-interface",
 "sp-state-machine",
 "sp-tracing",
 "sp-trie",
 "tracing",
 "tracing-core",
]

[[package]]
name = "sp-keyring"
version = "41.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c601d506585c0bcee79dbde401251b127af5f04c7373fc3cf7d6a6b7f6b970a3"
dependencies = [
 "sp-core",
 "sp-runtime",
 "strum 0.26.3",
]

[[package]]
name = "sp-keystore"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45f893398a5330e28f219662c7a0afa174fb068d8f82d2a9990016c4b0bc4369"
dependencies = [
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "sp-core",
 "sp-externalities",
]

[[package]]
name = "sp-maybe-compressed-blob"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0c768c11afbe698a090386876911da4236af199cd38a5866748df4d8628aeff"
dependencies = [
 "thiserror 1.0.69",
 "zstd 0.12.4",
]

[[package]]
name = "sp-metadata-ir"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82d1db25e362edbf5531b427d4bdfc2562bec6a031c3eb2a9145c0a0a01a572d"
dependencies = [
 "frame-metadata 20.0.0",
 "parity-scale-codec",
 "scale-info",
]

[[package]]
name = "sp-mixnet"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e65fb51d9ff444789b3c7771a148d7b685ec3c02498792fd0ecae0f1e00218f"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api",
 "sp-application-crypto",
]

[[package]]
name = "sp-offchain"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe5ac60e48200d7b7f61681320deaf06bdced47cfd5f1cb4589b533b58fa4da4"
dependencies = [
 "sp-api",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "sp-panic-handler"
version = "13.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8b52e69a577cbfdea62bfaf16f59eb884422ce98f78b5cd8d9bf668776bced1"
dependencies = [
 "backtrace",
 "regex",
]

[[package]]
name = "sp-rpc"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0acde213e9f08065dcc407a934e9ffd5388bef51347326195405efb62c7a0e4a"
dependencies = [
 "rustc-hash 1.1.0",
 "serde",
 "sp-core",
]

[[package]]
name = "sp-runtime"
version = "41.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3864101a28faba3d8eca026e3f56ea20dd1d979ce1bcc20152e86c9d82be52bf"
dependencies = [
 "binary-merkle-tree",
 "docify",
 "either",
 "hash256-std-hasher",
 "impl-trait-for-tuples",
 "log",
 "num-traits",
 "parity-scale-codec",
 "paste",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "simple-mermaid",
 "sp-application-crypto",
 "sp-arithmetic",
 "sp-core",
 "sp-io",
 "sp-std",
 "sp-trie",
 "sp-weights",
 "tracing",
 "tuplex",
]

[[package]]
name = "sp-runtime-interface"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e99db36a7aff44c335f5d5b36c182a3e0cac61de2fefbe2eeac6af5fb13f63bf"
dependencies = [
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "polkavm-derive",
 "primitive-types",
 "sp-externalities",
 "sp-runtime-interface-proc-macro",
 "sp-std",
 "sp-storage",
 "sp-tracing",
 "sp-wasm-interface",
 "static_assertions",
]

[[package]]
name = "sp-runtime-interface-proc-macro"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0195f32c628fee3ce1dfbbf2e7e52a30ea85f3589da9fe62a8b816d70fc06294"
dependencies = [
 "Inflector",
 "expander",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sp-session"
version = "38.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a4158c5558192b56cf5ba2ea028cbdbf0fc7c65258e5aa7653bdfad6e68ed21"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
 "sp-staking",
]

[[package]]
name = "sp-staking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f8f9c0a32836e3c8842b0aec0813077654885d45d83b618210fbb730ea63545"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "sp-state-machine"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "206508475c01ae2e14f171d35d7fc3eaa7278140d7940416591d49a784792ed6"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "smallvec",
 "sp-core",
 "sp-externalities",
 "sp-panic-handler",
 "sp-trie",
 "thiserror 1.0.69",
 "tracing",
 "trie-db",
]

[[package]]
name = "sp-statement-store"
version = "20.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6633564ef0b4179c3109855b8480673dea40bd0c11a46e89fa7b7fc526e65de"
dependencies = [
 "aes-gcm",
 "curve25519-dalek",
 "ed25519-dalek",
 "hkdf",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "sha2 0.10.9",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-crypto-hashing",
 "sp-externalities",
 "sp-runtime",
 "sp-runtime-interface",
 "thiserror 1.0.69",
 "x25519-dalek",
]

[[package]]
name = "sp-std"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12f8ee986414b0a9ad741776762f4083cd3a5128449b982a3919c4df36874834"

[[package]]
name = "sp-storage"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee3b70ca340e41cde9d2e069d354508a6e37a6573d66f7cc38f11549002f64ec"
dependencies = [
 "impl-serde",
 "parity-scale-codec",
 "ref-cast",
 "serde",
 "sp-debug-derive",
]

[[package]]
name = "sp-timestamp"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "176c77326c15425a15e085261161a9435f9a3c0d4bf61dae6dccf05b957a51c6"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "sp-inherents",
 "sp-runtime",
 "thiserror 1.0.69",
]

[[package]]
name = "sp-tracing"
version = "17.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6147a5b8c98b9ed4bf99dc033fab97a468b4645515460974c8784daeb7c35433"
dependencies = [
 "parity-scale-codec",
 "tracing",
 "tracing-core",
 "tracing-subscriber",
]

[[package]]
name = "sp-transaction-pool"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05fe2b97ebbbdbaab7200914f5fa3e3493972fceb39d3fb9324bc5b63f60a994"
dependencies = [
 "sp-api",
 "sp-runtime",
]

[[package]]
name = "sp-transaction-storage-proof"
version = "36.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49fc175a54170879cc504306e08650d96091e4b9f033b20f4ee542dc9b61b5fd"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-trie",
]

[[package]]
name = "sp-trie"
version = "39.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a555bf4c42ca89e2e7bf2f11308806dad13cdbd7f8fd60cf2649f12b6ee809bf"
dependencies = [
 "ahash",
 "hash-db",
 "memory-db",
 "nohash-hasher",
 "parity-scale-codec",
 "parking_lot 0.12.3",
 "rand 0.8.5",
 "scale-info",
 "schnellru",
 "sp-core",
 "sp-externalities",
 "thiserror 1.0.69",
 "tracing",
 "trie-db",
 "trie-root",
]

[[package]]
name = "sp-version"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd736a15ff2ea0a67c5a3bbdfd842d88f11f0774d7701a8d8a316f8deba276c5"
dependencies = [
 "impl-serde",
 "parity-scale-codec",
 "parity-wasm",
 "scale-info",
 "serde",
 "sp-crypto-hashing-proc-macro",
 "sp-runtime",
 "sp-std",
 "sp-version-proc-macro",
 "thiserror 1.0.69",
]

[[package]]
name = "sp-version-proc-macro"
version = "15.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54cabc8279e835cd9c608d70cb00e693bddec94fe8478e9f3104dad1da5f93ca"
dependencies = [
 "parity-scale-codec",
 "proc-macro-warning",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sp-wasm-interface"
version = "21.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b066baa6d57951600b14ffe1243f54c47f9c23dd89c262e17ca00ae8dca58be9"
dependencies = [
 "anyhow",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "wasmtime",
]

[[package]]
name = "sp-weights"
version = "31.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "515aa194eabac059041df2dbee75b059b99981213ec680e9de85b45b6988346a"
dependencies = [
 "bounded-collections",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "smallvec",
 "sp-arithmetic",
 "sp-debug-derive",
]

[[package]]
name = "spin"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spinning_top"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d96d2d1d716fb500937168cc09353ffdc7a012be8475ac7308e1bdf0e3923300"
dependencies = [
 "lock_api",
]

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "ss58-registry"
version = "1.51.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19409f13998e55816d1c728395af0b52ec066206341d939e22e7766df9b494b8"
dependencies = [
 "Inflector",
 "num-format",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "unicode-xid",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "staging-xcm"
version = "16.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dead7481ba2dec11b0df89745cef3a76f3eef9c9df20155426cd7e9651b4c799"
dependencies = [
 "array-bytes",
 "bounded-collections",
 "derive-where",
 "environmental",
 "frame-support",
 "hex-literal",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-runtime",
 "sp-weights",
 "xcm-procedural",
]

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "static_init"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a2a1c578e98c1c16fc3b8ec1328f7659a500737d7a0c6d625e73e830ff9c1f6"
dependencies = [
 "bitflags 1.3.2",
 "cfg_aliases 0.1.1",
 "libc",
 "parking_lot 0.11.2",
 "parking_lot_core 0.8.6",
 "static_init_macro",
 "winapi",
]

[[package]]
name = "static_init_macro"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1389c88ddd739ec6d3f8f83343764a0e944cd23cfbf126a9796a714b0b6edd6f"
dependencies = [
 "cfg_aliases 0.1.1",
 "memchr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "string-interner"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c6a0d765f5807e98a091107bae0a56ea3799f66a5de47b2c84c94a39c09974e"
dependencies = [
 "cfg-if",
 "hashbrown 0.14.5",
]

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"

[[package]]
name = "strum"
version = "0.26.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fec0f0aef304996cf250b31b5a10dee7980c85da9d759361292b8bca5a18f06"
dependencies = [
 "strum_macros 0.26.4",
]

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c6bee85a5a24955dc440386795aa378cd9cf82acd5f764469152d2270e581be"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "substrate-bip39"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca58ffd742f693dc13d69bdbb2e642ae239e0053f6aab3b104252892f856700a"
dependencies = [
 "hmac 0.12.1",
 "pbkdf2",
 "schnorrkel",
 "sha2 0.10.9",
 "zeroize",
]

[[package]]
name = "substrate-build-script-utils"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b285e7d183a32732fdc119f3d81b7915790191fad602b7c709ef247073c77a2e"

[[package]]
name = "substrate-frame-rpc-system"
version = "43.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "619d33f2bb5f1607f9073246163601c45f66044e85ade06bcb83feebf303ecd3"
dependencies = [
 "docify",
 "frame-system-rpc-runtime-api",
 "futures",
 "jsonrpsee",
 "log",
 "parity-scale-codec",
 "sc-rpc-api",
 "sc-transaction-pool-api",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "substrate-prometheus-endpoint"
version = "0.17.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe1fee79cb0bf260bb84b4fa885fae887646894a971abddae3d9ac4921531540"
dependencies = [
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "log",
 "prometheus",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "substrate-wasm-builder"
version = "26.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1adc17ecd661e16b25708f36f6e6961f809a3ab16c89132a4acd7936c0f31e46"
dependencies = [
 "array-bytes",
 "build-helper",
 "cargo_metadata",
 "console",
 "filetime",
 "frame-metadata 20.0.0",
 "jobserver",
 "merkleized-metadata",
 "parity-scale-codec",
 "parity-wasm",
 "polkavm-linker",
 "sc-executor",
 "shlex",
 "sp-core",
 "sp-io",
 "sp-maybe-compressed-blob",
 "sp-tracing",
 "sp-version",
 "strum 0.26.3",
 "tempfile",
 "toml 0.8.22",
 "walkdir",
 "wasm-opt",
]

[[package]]
name = "subtle"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d67a5a62ba6e01cb2192ff309324cb4875d0c451d55fe2319433abe7a05a8ee"

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "*****************************************c51ccc10d74c157bdea3292"

[[package]]
name = "subxt"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c17d7ec2359d33133b63c97e28c8b7cd3f0a5bc6ce567ae3aef9d9e85be3433"
dependencies = [
 "async-trait",
 "derive-where",
 "either",
 "frame-metadata 17.0.0",
 "futures",
 "hex",
 "impl-serde",
 "jsonrpsee",
 "parity-scale-codec",
 "polkadot-sdk",
 "primitive-types",
 "scale-bits",
 "scale-decode 0.14.0",
 "scale-encode",
 "scale-info",
 "scale-value",
 "serde",
 "serde_json",
 "subxt-core",
 "subxt-lightclient",
 "subxt-macro",
 "subxt-metadata",
 "thiserror 1.0.69",
 "tokio",
 "tokio-util",
 "tracing",
 "url",
 "web-time",
]

[[package]]
name = "subxt-codegen"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6550ef451c77db6e3bc7c56fb6fe1dca9398a2c8fc774b127f6a396a769b9c5b"
dependencies = [
 "heck 0.5.0",
 "parity-scale-codec",
 "proc-macro2",
 "quote",
 "scale-info",
 "scale-typegen",
 "subxt-metadata",
 "syn 2.0.101",
 "thiserror 1.0.69",
]

[[package]]
name = "subxt-core"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7a1bc6c9c1724971636a66e3225a7253cdb35bb6efb81524a6c71c04f08c59"
dependencies = [
 "base58",
 "blake2 0.10.6",
 "derive-where",
 "frame-decode",
 "frame-metadata 17.0.0",
 "hashbrown 0.14.5",
 "hex",
 "impl-serde",
 "keccak-hash",
 "parity-scale-codec",
 "polkadot-sdk",
 "primitive-types",
 "scale-bits",
 "scale-decode 0.14.0",
 "scale-encode",
 "scale-info",
 "scale-value",
 "serde",
 "serde_json",
 "subxt-metadata",
 "tracing",
]

[[package]]
name = "subxt-lightclient"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89ebc9131da4d0ba1f7814495b8cc79698798ccd52cacd7bcefe451e415bd945"
dependencies = [
 "futures",
 "futures-util",
 "serde",
 "serde_json",
 "smoldot-light",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "subxt-macro"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7819c5e09aae0319981ee853869f2fcd1fac4db8babd0d004c17161297aadc05"
dependencies = [
 "darling",
 "parity-scale-codec",
 "proc-macro-error2",
 "quote",
 "scale-typegen",
 "subxt-codegen",
 "subxt-utils-fetchmetadata",
 "syn 2.0.101",
]

[[package]]
name = "subxt-metadata"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aacd4e7484fef58deaa2dcb32d94753a864b208a668c0dd0c28be1d8abeeadb2"
dependencies = [
 "frame-decode",
 "frame-metadata 17.0.0",
 "hashbrown 0.14.5",
 "parity-scale-codec",
 "polkadot-sdk",
 "scale-info",
]

[[package]]
name = "subxt-signer"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d680352d04665b1e4eb6f9d2a54b800c4d8e1b20478e69be1b7d975b08d9fc34"
dependencies = [
 "base64 0.22.1",
 "bip32",
 "bip39",
 "cfg-if",
 "crypto_secretbox",
 "hex",
 "hmac 0.12.1",
 "keccak-hash",
 "parity-scale-codec",
 "pbkdf2",
 "polkadot-sdk",
 "regex",
 "schnorrkel",
 "scrypt",
 "secp256k1 0.30.0",
 "secrecy 0.10.3",
 "serde",
 "serde_json",
 "sha2 0.10.9",
 "subxt-core",
 "zeroize",
]

[[package]]
name = "subxt-utils-fetchmetadata"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3c53bc3eeaacc143a2f29ace4082edd2edaccab37b69ad20befba9fb00fdb3d"
dependencies = [
 "hex",
 "parity-scale-codec",
 "thiserror 1.0.69",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.101"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce2b7fc941b3a24138a0a7cf8e858bfc6a992e7978a068a5c760deb0ed43caf"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "synstructure"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36bdaa60a83aca3921b5259d5400cbf5e90fc51931376a9bd4a0eb79aa7210f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "unicode-xid",
]

[[package]]
name = "synstructure"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "728a70f3dbaf5bab7f0c4b1ac8d7ae5ea60a4b5549c8a5914361c99147a709d2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "system-configuration"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c879d448e9d986b661742763247d3693ed13609438cf3d006f51f5368a5ba6b"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "system-configuration-sys",
]

[[package]]
name = "system-configuration-sys"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e1d1b10ced5ca923a1fcb8d03e96b8d3268065d724548c0211415ff6ac6bac4"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61c41af27dd6d1e27b1b16b489db798443478cef1f06a660c96db617ba5de3b1"

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand",
 "getrandom 0.3.3",
 "once_cell",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "terminal_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45c6481c4829e4cc63825e62c49186a34538b7b2750b73b266581ffb612fb5ed"
dependencies = [
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "termtree"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f50febec83f5ee1df3015341d8bd429f2d1cc62bcba7ea2076759d315084683"

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl 1.0.69",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thousands"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bf63baf9f5039dadc247375c29eb13706706cfde997d0330d05aa63a77d8820"

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "threadpool"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d050e60b33d41c19108b32cea32164033a9013fe3b46cbd4457559bfbf77afaa"
dependencies = [
 "num_cpus",
]

[[package]]
name = "tikv-jemalloc-ctl"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "619bfed27d807b54f7f776b9430d4f8060e66ee138a28632ca898584d462c31c"
dependencies = [
 "libc",
 "paste",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.5.4****.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9402443cb8fd499b6f327e40565234ff34dbda27460c5b47db0db77443dd85d1"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "time"
version = "0.3.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a7619e19bc266e0f9c5e6686659d394bc57973859340060a69221e57dbc0c40"
dependencies = [
 "deranged",
 "itoa",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9e9a38711f559d9e3ce1cdb06dd7c5b8ea546bc90052da6d06bb76da74bb07c"

[[package]]
name = "time-macros"
version = "0.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3526739392ec93fd8b359c8e98514cb3e8e021beb4e5f597b00a0221f8ed8a49"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinystr"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d4f6d1145dcb577acf783d4e601bc1d76a13337bb54e6233add580b07344c8b"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2513ca694ef9ede0fb23fe71a4ee4107cb102b9dc1930f6d0fd77aae068ae165"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio",
 "parking_lot 0.12.3",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tokio-rustls"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e727b36a1a0e8b74c376ac2211e40c2c8af09fb4013c60d910495810f008e9b"
dependencies = [
 "rustls",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
 "tokio-util",
]

[[package]]
name = "tokio-tungstenite"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a9daff607c6d2bf6c16fd681ccb7eecc83e4e2cdc1ca067ffaadfca5de7f084"
dependencies = [
 "futures-util",
 "log",
 "rustls",
 "rustls-native-certs",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls",
 "tungstenite",
]

[[package]]
name = "tokio-util"
version = "0.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66a539a9ad6d5d281510d5bd368c973d636c02dbf8a67300bfb6b950696ad7df"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "toml"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05ae329d1f08c4d17a59bed7ff5b5a769d062e64a62d34a3261b219e62cd5aae"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit",
]

[[package]]
name = "toml_datetime"
version = "0.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da5db5a963e24bc68be8b17b6fa82814bb22ee8660f192bb182771d498f09a3"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.22.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "310068873db2c5b3e7659d2cc35d21855dbafa50d1ce336397c666e3cb08137e"
dependencies = [
 "indexmap 2.9.0",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_write",
 "winnow",
]

[[package]]
name = "toml_write"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfb942dfe1d8e29a7ee7fcbde5bd2b9a25fb89aa70caea2eba3bee836ff41076"

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project",
 "pin-project-lite",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9cd434a998747dd2c4276bc96ee2e0c7a2eadf3cae88e52be55a05fa9053f5"
dependencies = [
 "bitflags 2.9.0",
 "bytes",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "pin-project-lite",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "121c2a6cda46980bb0fcd1647ffaf6cd3fc79a013de288782836f6df9c48780e"

[[package]]
name = "tower-service"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df9b6e13f2d32c91b9bd719c00d1958837bc7dec474d94952798cc8e69eeec3"

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395ae124c09f9e6918a2310af6038fba074bcf474ac352496d5910dd59a2226d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tracing-core"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e672c95779cf947c5311f83787af4fa8fffd12fb27e4993211a84bdfd9610f9c"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracing-gum"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3a8f8a5b9157a1a473ffc8f2395b828a4238ed4b15044a9f861573f98049418"
dependencies = [
 "coarsetime",
 "polkadot-primitives",
 "tracing",
 "tracing-gum-proc-macro",
]

[[package]]
name = "tracing-gum-proc-macro"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f074568687ffdfd0adb6005aa8d1d96840197f2c159f80471285f08694cf0ce"
dependencies = [
 "expander",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8189decb5ac0fa7bc8b96b7cb9b2701d60d48805aca84a238004d665fcc4008"
dependencies = [
 "matchers",
 "nu-ansi-term",
 "once_cell",
 "parking_lot 0.12.3",
 "regex",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "time",
 "tracing",
 "tracing-core",
 "tracing-log",
]

[[package]]
name = "trie-db"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c0670ab45a6b7002c7df369fee950a27cf29ae0474343fd3a15aa15f691e7a6"
dependencies = [
 "hash-db",
 "log",
 "rustc-hex",
 "smallvec",
]

[[package]]
name = "trie-root"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4ed310ef5ab98f5fa467900ed906cb9232dd5376597e00fd4cba2a449d06c0b"
dependencies = [
 "hash-db",
]

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "tt-call"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f195fd851901624eee5a58c4bb2b4f06399148fcd0ed336e6f1cb60a9881df"

[[package]]
name = "tungstenite"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4793cb5e56680ecbb1d843515b23b6de9a75eb04b66643e256a396d43be33c13"
dependencies = [
 "bytes",
 "data-encoding",
 "http 1.3.1",
 "httparse",
 "log",
 "rand 0.9.1",
 "rustls",
 "rustls-pki-types",
 "sha1",
 "thiserror 2.0.12",
 "url",
 "utf-8",
]

[[package]]
name = "tuplex"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "676ac81d5454c4dcf37955d34fa8626ede3490f744b86ca14a7b90168d2a08aa"

[[package]]
name = "twox-hash"
version = "1.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fee6b57c6a41524a810daee9286c02d7752c4253064d0b05472833a438f675"
dependencies = [
 "cfg-if",
 "digest 0.10.7",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "typenum"
version = "1.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1dccffe3ce07af9386bfd29e80c0ab1a8205a2fc34e4bcd40364df902cfa8f3f"

[[package]]
name = "ucd-trie"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2896d95c02a80c6d6a5d6e953d479f5ddf2dfdb6a244441010e373ac0fb88971"

[[package]]
name = "uint"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f64bba2c53b04fcab63c01a7d7427eadc821e3bc48c34dc9ba29c501164b52"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "uint"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "909988d098b2f738727b161a106cfc7cab00c539c2687a8836f8e565976fb53e"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unicode-ident"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a5f39404a5da50712a4c1eecf25e90dd62b613502b7e925fd4e4d19b5c96512"

[[package]]
name = "unicode-normalization"
version = "0.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c5713f0fc4b5db668a2ac63cdb7bb4469d8c9fed047b1d0292cc7b0ce2ba921"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle 2.6.1",
]

[[package]]
name = "unsigned-varint"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6889a77d49f1f013504cec6bf97a2c730394adedaeb1deb5ea08949a50541105"
dependencies = [
 "asynchronous-codec 0.6.2",
 "bytes",
 "futures-io",
 "futures-util",
]

[[package]]
name = "unsigned-varint"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb066959b24b5196ae73cb057f45598450d2c5f71460e98c49b738086eff9c06"
dependencies = [
 "bytes",
 "tokio-util",
]

[[package]]
name = "untrusted"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
]

[[package]]
name = "utf-8"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09cc8ee72d2a9becf2f2febe0205bbed8fc6615b7cb429ad062dc7b7ddd036a9"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "valuable"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba73ea9cf16a25df0c8caa16c51acb937d5712a8429db78a3ee29d5dcacd3a65"

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "version_check"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b928f33d975fc6ad9f86c8f283853ad26bdd5b10b7f1542aa2fa15e2289105a"

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "w3f-bls"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6bfb937b3d12077654a9e43e32a4e9c20177dd9fea0f3aba673e7840bb54f32"
dependencies = [
 "ark-bls12-377",
 "ark-bls12-381 0.4.0",
 "ark-ec 0.4.2",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-serialize-derive 0.4.2",
 "arrayref",
 "digest 0.10.7",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "sha2 0.10.9",
 "sha3",
 "zeroize",
]

[[package]]
name = "w3f-pcs"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbe7a8d5c914b69392ab3b267f679a2e546fe29afaddce47981772ac71bd02e1"
dependencies = [
 "ark-ec 0.5.0",
 "ark-ff 0.5.0",
 "ark-poly 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "merlin",
]

[[package]]
name = "w3f-plonk-common"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aca389e494fe08c5c108b512e2328309036ee1c0bc7bdfdb743fef54d448c8c"
dependencies = [
 "ark-ec 0.5.0",
 "ark-ff 0.5.0",
 "ark-poly 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "getrandom_or_panic",
 "rand_core 0.6.4",
 "w3f-pcs",
]

[[package]]
name = "w3f-ring-proof"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a639379402ad51504575dbd258740383291ac8147d3b15859bdf1ea48c677de"
dependencies = [
 "ark-ec 0.5.0",
 "ark-ff 0.5.0",
 "ark-poly 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "ark-transcript",
 "w3f-pcs",
 "w3f-plonk-common",
]

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasix"
version = "0.12.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1fbb4ef9bbca0c1170e0b00dd28abc9e3b68669821600cad1caaed606583c6d"
dependencies = [
 "wasi 0.11.0+wasi-snapshot-preview1",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "wasm-instrument"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a47ecb37b9734d1085eaa5ae1a81e60801fd8c28d4cabdd8aedb982021918bc"
dependencies = [
 "parity-wasm",
]

[[package]]
name = "wasm-opt"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fd87a4c135535ffed86123b6fb0f0a5a0bc89e50416c942c5f0662c645f679c"
dependencies = [
 "anyhow",
 "libc",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "tempfile",
 "thiserror 1.0.69",
 "wasm-opt-cxx-sys",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-cxx-sys"
version = "0.116.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c57b28207aa724318fcec6575fe74803c23f6f266fce10cbc9f3f116762f12e"
dependencies = [
 "anyhow",
 "cxx",
 "cxx-build",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-sys"
version = "0.116.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a1cce564dc768dacbdb718fc29df2dba80bd21cb47d8f77ae7e3d95ceb98cbe"
dependencies = [
 "anyhow",
 "cc",
 "cxx",
 "cxx-build",
]

[[package]]
name = "wasm-timer"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be0ecb0db480561e9a7642b5d3e4187c128914e58aa84330b9493e3eb68c5e7f"
dependencies = [
 "futures",
 "js-sys",
 "parking_lot 0.11.2",
 "pin-utils",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wasmi"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50386c99b9c32bd2ed71a55b6dd4040af2580530fae8bdb9a6576571a80d0cca"
dependencies = [
 "arrayvec 0.7.6",
 "multi-stash",
 "num-derive",
 "num-traits",
 "smallvec",
 "spin 0.9.8",
 "wasmi_collections",
 "wasmi_core",
 "wasmparser-nostd",
]

[[package]]
name = "wasmi_collections"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c128c039340ffd50d4195c3f8ce31aac357f06804cfc494c8b9508d4b30dca4"
dependencies = [
 "ahash",
 "hashbrown 0.14.5",
 "string-interner",
]

[[package]]
name = "wasmi_core"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a23b3a7f6c8c3ceeec6b83531ee61f0013c56e51cbf2b14b0f213548b23a4b41"
dependencies = [
 "downcast-rs",
 "libm",
 "num-traits",
 "paste",
]

[[package]]
name = "wasmparser"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48134de3d7598219ab9eaf6b91b15d8e50d31da76b8519fe4ecfcec2cf35104b"
dependencies = [
 "indexmap 1.9.3",
 "url",
]

[[package]]
name = "wasmparser-nostd"
version = "0.100.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5a015fe95f3504a94bb1462c717aae75253e39b9dd6c3fb1062c934535c64aa"
dependencies = [
 "indexmap-nostd",
]

[[package]]
name = "wasmtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f907fdead3153cb9bfb7a93bbd5b62629472dc06dee83605358c64c52ed3dda9"
dependencies = [
 "anyhow",
 "bincode",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "object 0.30.4",
 "once_cell",
 "paste",
 "psm",
 "rayon",
 "serde",
 "target-lexicon",
 "wasmparser",
 "wasmtime-cache",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "wasmtime-jit",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-asm-macros"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3b9daa7c14cd4fa3edbf69de994408d5f4b7b0959ac13fa69d465f6597f810d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "wasmtime-cache"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c86437fa68626fe896e5afc69234bb2b5894949083586535f200385adfd71213"
dependencies = [
 "anyhow",
 "base64 0.21.7",
 "bincode",
 "directories-next",
 "file-per-thread-logger",
 "log",
 "rustix 0.36.17",
 "serde",
 "sha2 0.10.9",
 "toml 0.5.11",
 "windows-sys 0.45.0",
 "zstd 0.11.2+zstd.1.5.2",
]

[[package]]
name = "wasmtime-cranelift"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1cefde0cce8cb700b1b21b6298a3837dba46521affd7b8c38a9ee2c869eee04"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "cranelift-native",
 "cranelift-wasm",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "target-lexicon",
 "thiserror 1.0.69",
 "wasmparser",
 "wasmtime-cranelift-shared",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-cranelift-shared"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd041e382ef5aea1b9fc78442394f1a4f6d676ce457e7076ca4cb3f397882f8b"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-native",
 "gimli 0.27.3",
 "object 0.30.4",
 "target-lexicon",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-environ"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a990198cee4197423045235bf89d3359e69bd2ea031005f4c2d901125955c949"
dependencies = [
 "anyhow",
 "cranelift-entity",
 "gimli 0.27.3",
 "indexmap 1.9.3",
 "log",
 "object 0.30.4",
 "serde",
 "target-lexicon",
 "thiserror 1.0.69",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "wasmtime-jit"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0de48df552cfca1c9b750002d3e07b45772dd033b0b206d5c0968496abf31244"
dependencies = [
 "addr2line 0.19.0",
 "anyhow",
 "bincode",
 "cfg-if",
 "cpp_demangle",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "rustc-demangle",
 "serde",
 "target-lexicon",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "wasmtime-jit-icache-coherence",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-jit-debug"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e0554b84c15a27d76281d06838aed94e13a77d7bf604bbbaf548aa20eb93846"
dependencies = [
 "object 0.30.4",
 "once_cell",
 "rustix 0.36.17",
]

[[package]]
name = "wasmtime-jit-icache-coherence"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aecae978b13f7f67efb23bd827373ace4578f2137ec110bbf6a4a7cde4121bbd"
dependencies = [
 "cfg-if",
 "libc",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-runtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658cf6f325232b6760e202e5255d823da5e348fdea827eff0a2a22319000b441"
dependencies = [
 "anyhow",
 "cc",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "mach",
 "memfd",
 "memoffset",
 "paste",
 "rand 0.8.5",
 "rustix 0.36.17",
 "wasmtime-asm-macros",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-types"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4f6fffd2a1011887d57f07654dd112791e872e3ff4a2e626aee8059ee17f06f"
dependencies = [
 "cranelift-entity",
 "serde",
 "thiserror 1.0.69",
 "wasmparser",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki-root-certs"
version = "0.26.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75c7f0ef91146ebfb530314f5f1d24528d7f0767efbfd31dce919275413e393e"
dependencies = [
 "webpki-root-certs 1.0.0",
]

[[package]]
name = "webpki-root-certs"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01a83f7e1a9f8712695c03eabe9ed3fbca0feff0152f33f12593e5a6303cb1a4"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "webpki-roots"
version = "0.25.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f20c57d8d7db6d3b86154206ae5d8fba62dd39573114de97c2cb0578251f8e1"

[[package]]
name = "wide"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41b5576b9a81633f3e8df296ce0063042a73507636cbe956c61133dd7034ab22"
dependencies = [
 "bytemuck",
 "safe_arch",
]

[[package]]
name = "widestring"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd7cf3379ca1aac9eea11fba24fd7e315d621f8dfe35c8d7d2be8b793726e07d"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efc5cf48f83140dcaab716eeaea345f9e93d0018fb81162753a3f76c3397b538"
dependencies = [
 "windows-core 0.53.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dcc5b895a6377f1ab9fa55acedab1fd5ac0db66ad1e6c7f47e28a22e446a5dd"
dependencies = [
 "windows-result 0.1.2",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.61.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4763c1de310c86d75a878046489e2e5ba02c649d185f21c67d4cf8a56d098980"
dependencies = [
 "windows-implement",
 "windows-interface",
 "windows-link",
 "windows-result 0.3.2",
 "windows-strings",
]

[[package]]
name = "windows-implement"
version = "0.60.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a47fddd13af08290e67f4acabf4b459f647552718f683a7b415d290ac744a836"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.59.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd9211b69f8dcdfa817bfd14bf1c97c9188afa36f4750130fcdf3f400eca9fa8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-link"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76840935b766e1b0a05c0066835fb9ec80071d4c09a16f6bd5f7e655e3c14c38"

[[package]]
name = "windows-result"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e383302e8ec8515204254685643de10811af0ed97ea37210dc26fb0032647f8"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c64fd11a4fd95df68efcfee5f44a294fe71b8bc6a91993e2791938abcc712252"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-strings"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a2ba9642430ee452d5a7aa78d72907ebe8cfda358e8cb7918a2050581322f97"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm 0.52.6",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1e4c7e8ceaaf9cb7d7507c974735728ab453b67ef8f18febdd7c11fe59dca8b"
dependencies = [
 "windows_aarch64_gnullvm 0.53.0",
 "windows_aarch64_msvc 0.53.0",
 "windows_i686_gnu 0.53.0",
 "windows_i686_gnullvm 0.53.0",
 "windows_i686_msvc 0.53.0",
 "windows_x86_64_gnu 0.53.0",
 "windows_x86_64_gnullvm 0.53.0",
 "windows_x86_64_msvc 0.53.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b8d5f90ddd19cb4a147a5fa63ca848db3df085e25fee3cc10b39b6eebae764"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_aarch64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7651a1f62a11b8cbd5e0d42526e55f2c99886c77e007179efff86c2b137e66c"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1dc67659d35f387f5f6c479dc4e28f1d4bb90ddd1a5d3da2e5d97b42d6272c3"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce6ccbdedbf6d6354471319e781c0dfef054c81fbc7cf83f338a4296c0cae11"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_i686_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581fee95406bb13382d2f65cd4a908ca7b1e4c2f1917f143ba16efe98a589b5d"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e55b5ac9ea33f2fc1716d1742db15574fd6fc8dadc51caab1c16a3d3b4190ba"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a6e035dd0599267ce1ee132e51c27dd29437f63325753051e71dd9e42406c57"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "windows_x86_64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271414315aff87387382ec3d271b52d7ae78726f5d44ac98b4f4030c91880486"

[[package]]
name = "winnow"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06928c8748d81b05c9be96aad92e1b6ff01833332f281e8cfca3be4b35fc9ec"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "writeable"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea2f10b9bb0928dfb1b42b65e1f9e36f7f54dbdf08457afefb38afcdec4fa2bb"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "x25519-dalek"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7e468321c81fb07fa7f4c636c3972b9100f0346e5b6a9f2bd0603a52f7ed277"
dependencies = [
 "curve25519-dalek",
 "rand_core 0.6.4",
 "serde",
 "zeroize",
]

[[package]]
name = "x509-parser"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcbc162f30700d6f3f82a24bf7cc62ffe7caea42c0b2cba8bf7f3ae50cf51f69"
dependencies = [
 "asn1-rs 0.6.2",
 "data-encoding",
 "der-parser 9.0.0",
 "lazy_static",
 "nom",
 "oid-registry 0.7.1",
 "rusticata-macros",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "x509-parser"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4569f339c0c402346d4a75a9e39cf8dad310e287eef1ff56d4c68e5067f53460"
dependencies = [
 "asn1-rs 0.7.1",
 "data-encoding",
 "der-parser 10.0.0",
 "lazy_static",
 "nom",
 "oid-registry 0.8.1",
 "rusticata-macros",
 "thiserror 2.0.12",
 "time",
]

[[package]]
name = "xcm-procedural"
version = "11.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d3d21c65cbf847ae0b1a8e6411b614d269d3108c6c649b039bffcf225e89aa4"
dependencies = [
 "Inflector",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "xml-rs"
version = "0.8.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a62ce76d9b56901b19a74f19431b0d8b3bc7ca4ad685a746dfd78ca8f4fc6bda"

[[package]]
name = "xmltree"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7d8a75eaf6557bb84a65ace8609883db44a29951042ada9b393151532e41fcb"
dependencies = [
 "xml-rs",
]

[[package]]
name = "yamux"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed0164ae619f2dc144909a9f082187ebb5893693d8c0196e8085283ccd4b776"
dependencies = [
 "futures",
 "log",
 "nohash-hasher",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "yamux"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17610762a1207ee816c6fadc29220904753648aba0a9ed61c7b8336e80a559c4"
dependencies = [
 "futures",
 "log",
 "nohash-hasher",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.8.5",
 "static_assertions",
 "web-time",
]

[[package]]
name = "yap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff4524214bc4629eba08d78ceb1d6507070cc0bcbbed23af74e19e6e924a24cf"

[[package]]
name = "yasna"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17bb3549cc1321ae1296b9cdc2698e2b6cb1992adfa19a8c72e5b7a738f44cd"
dependencies = [
 "time",
]

[[package]]
name = "yoke"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f41bb01b8226ef4bfd589436a297c53d118f65921786300e427be8d487695cc"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38da3c9736e16c5d3c8c597a9aaa5d1fa565d0532ae05e27c24aa62fb32c0ab6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure 0.13.2",
]

[[package]]
name = "zerocopy"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1702d9583232ddb9174e01bb7c15a2ab8fb1bc6f227aa1233858c351a3ba0cb"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28a6e20d751156648aa063f3800b706ee209a32c0b4d9f24be3d980b01be55ef"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure 0.13.2",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerotrie"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36f0bbd478583f79edad978b407914f61b2972f5af6fa089686016be8f9af595"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
]

[[package]]
name = "zerovec"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a05eb080e015ba39cc9e23bbe5e7fb04d5fb040350f99f34e338d5fdd294428"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b96237efa0c878c64bd89c436f661be4e46b2f3eff1ebb976f7ef2321d2f58f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a27595e173641171fc74a1232b7b1c7a7cb6e18222c11e9dfb9888fa424c53c"
dependencies = [
 "zstd-safe 6.0.6",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "6.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee98ffd0b48ee95e6c5168188e44a54550b1564d9d530ee21d5f0eaed1069581"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.15+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb81183ddd97d0c74cedf1d50d85c8d08c1b8b68ee863bdee9e706eedba1a237"
dependencies = [
 "cc",
 "pkg-config",
]
