Afrachain with WTTP: Sovereign Blockchain Internet
Overview
This project combines a custom Substrate-based blockchain ("Afrachain") with the Web3 Transport Protocol (WTTP) to create a sovereign, decentralized "internet" infrastructure. Afrachain serves as the standalone blockchain layer, while WTTP enables on-chain hosting and serving of web content. Optimizations include off-chain workers for data compression and storage, EVM compatibility via Frontier, and a browser extension for seamless usability.
Key Features:

Sovereign chain: Standalone Substrate node with custom token (AFRA).
On-chain web hosting: WTTP for HTTP-like methods on blockchain.
Performance mitigations: Off-chain workers with compression (snappy).
EVM integration: Deploy Solidity contracts on Substrate.
Browser support: Chrome extension for wttp:// URLs.
Security: Auditing plan included.

Prerequisites

Rust: 1.70+ with nightly toolchain.
Node.js: 18+ with npm/yarn.
Hardhat: For contract deployment.
Chrome: For extension testing.

Installation

# Install Rust via rustup
curl --proto '=https' --tlsv1.2 https://sh.rustup.rs -sSf | sh

# Add to PATH (restart terminal or run this)
source ~/.cargo/env

# Install nightly toolchain
rustup toolchain install nightly

# Verify installation
rustc --version

# Install Node.js 18
# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm

# Check version (upgrade if < 18)
node --version

Clone the repo: git clone https://github.com/your-repo/afrachain-wttp.git
Install Rust deps: cargo build --release
Install Node deps: npm install
(Optional) Python deps: pip install -r requirements.txt (for any utility scripts)

Building and Running

Build Substrate node: cargo build --release
Run local dev node: ./target/release/afrachain-node --dev
Deploy WTTP contract: npx hardhat run scripts/deploy.js --network afrachain
Load browser extension: In Chrome, go to chrome://extensions/, enable developer mode, and load the extension/ folder.

Usage

Upload web content: Use WTTP client scripts to PUT content to the deployed contract.
Browse: Install the extension, then visit wttp://<contract-address>/index.html in Chrome.
Off-chain optimization: Large uploads trigger off-chain workers for compression.

Auditing and Security
Follow the auditing plan:

Run cargo audit and slither . for vulnerabilities.
Test with cargo test and npx hardhat test.
Engage external auditors for production.

Challenges Mitigated

Performance: Off-chain workers + snappy compression.
Compatibility: Frontier pallet ensures EVM-WASM harmony.
Usability: Browser extension.
Security: Thorough tools and processes.

Contributing
Fork and PR. Issues welcome!
License
MIT
