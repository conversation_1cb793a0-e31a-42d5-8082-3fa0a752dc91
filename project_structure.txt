afrachain-wttp/
├── Cargo.lock              # Rust dependencies lock file
├── Cargo.toml              # Rust project manifest (for Substrate)
├── README.md               # Project documentation
├── requirements.txt        # Python dependencies (for optional scripts/tools)
├── hardhat.config.js       # Hardhat configuration for WTTP deployment
├── package.json            # Node.js dependencies (for WTTP/Hardhat)
├── package-lock.json       # Node.js dependencies lock
├── contracts/              # Solidity contracts for WTTP
│   └── AfrachainWTTP.sol   # Custom WTTP contract
├── scripts/                # Deployment and utility scripts
│   └── deploy.js           # Hardhat deployment script
├── runtime/                # Substrate runtime
│   ├── Cargo.toml          # Runtime manifest
│   └── src/
│       └── lib.rs          # Runtime code with pallets (EVM, off-chain, etc.)
├── node/                   # Substrate node
│   ├── Cargo.toml          # Node manifest
│   └── src/
│       └── chain_spec.rs   # Chain specification
├── pallets/                # Custom Substrate pallets (e.g., for off-chain integration)
│   └── wttp_offchain/
│       └── src/
│           └── lib.rs      # Off-chain worker pallet
├── extension/              # Browser extension for WTTP browsing
│   ├── manifest.json       # Extension manifest
│   ├── background.js       # Background script
│   └── content.js          # Content script (placeholder)
└── tests/                  # Tests for Rust, Solidity, and JS
    └── integration.test.js # Example Hardhat test